=== WinCBT-Biometric Error Log ===
Started: 2025-05-19 14:20:02
----------------------------------------
2025-05-19 14:20:02 [INFO] Error handler initialized
2025-05-19 14:20:02 [INFO] Read database path from config: db
2025-05-19 14:20:02 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 14:20:02 [INFO] Created Temporary files directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\tmp
2025-05-19 14:20:02 [INFO] Error handler initialized
2025-05-19 14:20:02 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 14:20:02 [ERROR] Cannot access Configuration file: Invalid option.
2025-05-19 14:20:02 [ERROR] Cannot access Configuration file at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\config.ini
2025-05-19 14:20:10 [ERROR] Cannot access Database configuration: Invalid option.
2025-05-19 14:20:10 [ERROR] Cannot access Database configuration at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\config.ini
2025-05-19 14:20:12 [ERROR] Cannot access Candidates database: Invalid option.
2025-05-19 14:20:12 [ERROR] Cannot access Candidates database at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\candidates.ini
2025-05-19 14:20:13 [ERROR] Cannot access Hardware configuration: Invalid option.
2025-05-19 14:20:13 [ERROR] Cannot access Hardware configuration at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:20:15 [ERROR] Cannot access Rooms configuration: Invalid option.
2025-05-19 14:20:15 [ERROR] Cannot access Rooms configuration at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:20:20 [ERROR] Cannot access Seat assignments: Invalid option.
2025-05-19 14:20:20 [ERROR] Cannot access Seat assignments at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\seat_assignments.ini
2025-05-19 14:20:23 [ERROR] Cannot access FFmpeg executable: Invalid option.
2025-05-19 14:20:23 [ERROR] Cannot access FFmpeg executable at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\bin\ffmpeg.exe
2025-05-19 14:20:38 [ERROR] Cannot access Default photo image: Invalid option.
2025-05-19 14:20:38 [ERROR] Cannot access Default photo image at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\default_photo.png
2025-05-19 14:20:40 [ERROR] Cannot access Default fingerprint image: Invalid option.
2025-05-19 14:20:40 [ERROR] Cannot access Default fingerprint image at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\default_fingerprint.png
2025-05-19 14:20:42 [ERROR] Cannot access Default signature image: Invalid option.
2025-05-19 14:20:42 [ERROR] Cannot access Default signature image at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\default_signature.png
2025-05-19 14:20:45 [ERROR] Cannot access Gray placeholder image: Invalid option.
2025-05-19 14:20:45 [ERROR] Cannot access Gray placeholder image at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\gray.png
2025-05-19 14:20:47 [ERROR] Some required files or directories are missing. The application may not function correctly.
2025-05-19 14:20:51 [INFO] Initializing application components
2025-05-19 14:20:51 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:20:51 [INFO] Hardware cache loaded with 10 entries
2025-05-19 14:20:51 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:20:51 [INFO] Room cache loaded with 3 entries
2025-05-19 14:20:51 [INFO] Loading seat assignments for date: 20250519
2025-05-19 14:20:51 [INFO] No seat assignments found for today (20250519)
2025-05-19 14:20:51 [INFO] Database manager initialized successfully
2025-05-19 14:20:51 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 14:20:51 [INFO] Webcam manager initialized
2025-05-19 14:20:51 [INFO] Webcam: Starting webcam...
2025-05-19 14:20:53 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 14:20:53 [INFO] Webcam started successfully
2025-05-19 14:20:53 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 14:20:53 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 14:20:53 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 14:20:53 [INFO] Post-exam mode is enabled
2025-05-19 14:20:53 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 14:20:53 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 14:20:53 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 14:20:53 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 14:20:53 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 14:20:53 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 14:20:53 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 14:20:53 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 14:20:53 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 14:20:53 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 14:20:53 [INFO] Config: Paths.dbPath = db
2025-05-19 14:21:10 [INFO] Error handler initialized
2025-05-19 14:21:10 [INFO] Read database path from config: db
2025-05-19 14:21:10 [INFO] Using database path: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 14:21:10 [INFO] Created Fingerprint templates directory: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\fpt\
2025-05-19 14:21:10 [INFO] Created Temporary files directory: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\tmp
2025-05-19 14:21:10 [INFO] Created empty Database configuration file: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\config.ini
2025-05-19 14:21:10 [INFO] Created empty Candidates database file: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\candidates.ini
2025-05-19 14:21:10 [INFO] Created empty Hardware configuration file: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:21:10 [INFO] Created empty Rooms configuration file: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:21:10 [INFO] Created empty Seat assignments file: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\seat_assignments.ini
2025-05-19 14:21:10 [INFO] Error handler initialized
2025-05-19 14:21:10 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 14:21:10 [WARNING] Temporary files directory not found at: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\tmp
2025-05-19 14:21:10 [INFO] Created directory: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\tmp
2025-05-19 14:21:10 [ERROR] Cannot access Configuration file: Invalid option.
2025-05-19 14:21:10 [ERROR] Cannot access Configuration file at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\config.ini
2025-05-19 14:21:25 [ERROR] Cannot access Database configuration: Invalid option.
2025-05-19 14:21:25 [ERROR] Cannot access Database configuration at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\config.ini
2025-05-19 14:21:29 [ERROR] Cannot access Candidates database: Invalid option.
2025-05-19 14:21:29 [ERROR] Cannot access Candidates database at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\candidates.ini
2025-05-19 14:21:31 [ERROR] Cannot access Hardware configuration: Invalid option.
2025-05-19 14:21:31 [ERROR] Cannot access Hardware configuration at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:21:31 [ERROR] Cannot access Rooms configuration: Invalid option.
2025-05-19 14:21:31 [ERROR] Cannot access Rooms configuration at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:21:32 [ERROR] Cannot access Seat assignments: Invalid option.
2025-05-19 14:21:32 [ERROR] Cannot access Seat assignments at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\seat_assignments.ini
2025-05-19 14:21:33 [ERROR] Cannot access FFmpeg executable: Invalid option.
2025-05-19 14:21:33 [ERROR] Cannot access FFmpeg executable at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\bin\ffmpeg.exe
2025-05-19 14:21:34 [ERROR] Cannot access Default photo image: Invalid option.
2025-05-19 14:21:34 [ERROR] Cannot access Default photo image at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\img\default_photo.png
2025-05-19 14:21:34 [ERROR] Cannot access Default fingerprint image: Invalid option.
2025-05-19 14:21:34 [ERROR] Cannot access Default fingerprint image at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\img\default_fingerprint.png
2025-05-19 14:21:35 [ERROR] Cannot access Default signature image: Invalid option.
2025-05-19 14:21:35 [ERROR] Cannot access Default signature image at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\img\default_signature.png
2025-05-19 14:21:36 [ERROR] Cannot access Gray placeholder image: Invalid option.
2025-05-19 14:21:36 [ERROR] Cannot access Gray placeholder image at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\img\gray.png
2025-05-19 14:21:37 [ERROR] Some required files or directories are missing. The application may not function correctly.
2025-05-19 14:21:40 [INFO] Initializing application components
2025-05-19 14:21:40 [INFO] Loading hardware cache from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:21:40 [INFO] Hardware cache loaded with 0 entries
2025-05-19 14:21:40 [INFO] Loading room cache from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:21:40 [INFO] Room cache loaded with 0 entries
2025-05-19 14:21:40 [INFO] Loading seat assignments for date: 20250519
2025-05-19 14:21:40 [INFO] No seat assignments found for today (20250519)
2025-05-19 14:21:40 [INFO] Database manager initialized successfully
2025-05-19 14:21:40 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 14:21:40 [INFO] Webcam manager initialized
2025-05-19 14:21:40 [INFO] Webcam: Starting webcam...
2025-05-19 14:21:41 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 14:21:41 [INFO] Webcam started successfully
2025-05-19 14:21:41 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 14:21:41 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 14:21:41 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 14:21:41 [INFO] Post-exam mode is enabled
2025-05-19 14:21:41 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 14:21:41 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 14:21:41 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 14:21:41 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 14:21:41 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 14:21:41 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 14:21:41 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 14:21:41 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 14:21:41 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 14:21:41 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 14:21:41 [INFO] Config: Paths.dbPath = db
2025-05-19 14:22:00 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 14:24:52 [INFO] Error handler initialized
2025-05-19 14:24:52 [INFO] Read database path from config: db
2025-05-19 14:24:52 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 14:24:52 [INFO] Error handler initialized
2025-05-19 14:24:52 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 14:24:52 [ERROR] Cannot access Configuration file: Invalid option.
2025-05-19 14:24:52 [ERROR] Cannot access Configuration file at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\config.ini
2025-05-19 14:25:31 [INFO] Error handler initialized
2025-05-19 14:25:31 [INFO] Read database path from config: db
2025-05-19 14:25:31 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 14:25:31 [INFO] Error handler initialized
2025-05-19 14:25:31 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 14:25:31 [ERROR] Cannot access Configuration file: Invalid option.
2025-05-19 14:25:31 [ERROR] Cannot access Configuration file at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\config.ini
2025-05-19 14:25:36 [ERROR] Cannot access Database configuration: Invalid option.
2025-05-19 14:25:36 [ERROR] Cannot access Database configuration at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\config.ini
2025-05-19 14:25:38 [ERROR] Cannot access Candidates database: Invalid option.
2025-05-19 14:25:38 [ERROR] Cannot access Candidates database at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\candidates.ini
2025-05-19 14:25:40 [ERROR] Cannot access Hardware configuration: Invalid option.
2025-05-19 14:25:40 [ERROR] Cannot access Hardware configuration at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:25:42 [ERROR] Cannot access Rooms configuration: Invalid option.
2025-05-19 14:25:42 [ERROR] Cannot access Rooms configuration at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:25:43 [ERROR] Cannot access Seat assignments: Invalid option.
2025-05-19 14:25:43 [ERROR] Cannot access Seat assignments at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\seat_assignments.ini
2025-05-19 14:25:46 [ERROR] Cannot access FFmpeg executable: Invalid option.
2025-05-19 14:25:46 [ERROR] Cannot access FFmpeg executable at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\bin\ffmpeg.exe
2025-05-19 14:25:48 [ERROR] Cannot access Default photo image: Invalid option.
2025-05-19 14:25:48 [ERROR] Cannot access Default photo image at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\default_photo.png
2025-05-19 14:25:52 [ERROR] Cannot access Default fingerprint image: Invalid option.
2025-05-19 14:25:52 [ERROR] Cannot access Default fingerprint image at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\default_fingerprint.png
2025-05-19 14:25:54 [ERROR] Cannot access Default signature image: Invalid option.
2025-05-19 14:25:54 [ERROR] Cannot access Default signature image at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\default_signature.png
2025-05-19 14:25:58 [ERROR] Cannot access Gray placeholder image: Invalid option.
2025-05-19 14:25:58 [ERROR] Cannot access Gray placeholder image at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\gray.png
2025-05-19 14:26:01 [ERROR] Some required files or directories are missing. The application may not function correctly.
2025-05-19 14:26:02 [INFO] Initializing application components
2025-05-19 14:26:02 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:26:03 [INFO] Hardware cache loaded with 10 entries
2025-05-19 14:26:03 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:26:03 [INFO] Room cache loaded with 3 entries
2025-05-19 14:26:03 [INFO] Loading seat assignments for date: 20250519
2025-05-19 14:26:03 [INFO] No seat assignments found for today (20250519)
2025-05-19 14:26:03 [INFO] Database manager initialized successfully
2025-05-19 14:26:03 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 14:26:03 [INFO] Webcam manager initialized
2025-05-19 14:26:03 [INFO] Webcam: Starting webcam...
2025-05-19 14:26:04 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 14:26:04 [INFO] Webcam started successfully
2025-05-19 14:26:04 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 14:26:04 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 14:26:04 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 14:26:04 [INFO] Post-exam mode is enabled
2025-05-19 14:26:04 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 14:26:04 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 14:26:04 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 14:26:04 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 14:26:04 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 14:26:04 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 14:26:04 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 14:26:04 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 14:26:04 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 14:26:04 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 14:26:04 [INFO] Config: Paths.dbPath = db
2025-05-19 14:29:42 [INFO] Application exiting: Single (Code: 0)
2025-05-19 14:29:42 [INFO] Error handler initialized
2025-05-19 14:29:42 [INFO] Read database path from config: db
2025-05-19 14:29:42 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 14:29:43 [INFO] Error handler initialized
2025-05-19 14:29:43 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 14:29:43 [INFO] Validating required files and directories
2025-05-19 14:29:43 [INFO] Validated directory: Database
2025-05-19 14:29:43 [INFO] Validated directory: Images
2025-05-19 14:29:43 [INFO] Validated directory: Logs
2025-05-19 14:29:43 [INFO] Validated directory: Temporary files
2025-05-19 14:29:43 [INFO] Validated directory: Database images
2025-05-19 14:29:43 [INFO] Validated directory: Candidate images
2025-05-19 14:29:43 [INFO] Validated directory: Fingerprint templates
2025-05-19 14:29:43 [INFO] All required files and directories validated successfully
2025-05-19 14:29:43 [INFO] Initializing application components
2025-05-19 14:29:43 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:29:43 [INFO] Hardware cache loaded with 10 entries
2025-05-19 14:29:43 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:29:43 [INFO] Room cache loaded with 3 entries
2025-05-19 14:29:43 [INFO] Loading seat assignments for date: 20250519
2025-05-19 14:29:43 [INFO] No seat assignments found for today (20250519)
2025-05-19 14:29:43 [INFO] Database manager initialized successfully
2025-05-19 14:29:43 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 14:29:43 [INFO] Webcam manager initialized
2025-05-19 14:29:43 [INFO] Webcam: Starting webcam...
2025-05-19 14:29:44 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 14:29:44 [INFO] Webcam started successfully
2025-05-19 14:29:44 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 14:29:44 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 14:29:44 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 14:29:44 [INFO] Post-exam mode is enabled
2025-05-19 14:29:44 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 14:29:44 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 14:29:44 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 14:29:44 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 14:29:44 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 14:29:44 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 14:29:44 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 14:29:44 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 14:29:44 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 14:29:44 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 14:29:44 [INFO] Config: Paths.dbPath = db
2025-05-19 14:30:27 [INFO] Error handler initialized
2025-05-19 14:30:27 [INFO] Read database path from config: db
2025-05-19 14:30:27 [INFO] Using database path: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 14:30:27 [INFO] Error handler initialized
2025-05-19 14:30:27 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 14:30:27 [INFO] Validating required files and directories
2025-05-19 14:30:27 [INFO] Validated directory: Database
2025-05-19 14:30:27 [INFO] Validated directory: Images
2025-05-19 14:30:27 [INFO] Validated directory: Logs
2025-05-19 14:30:27 [INFO] Validated directory: Temporary files
2025-05-19 14:30:27 [INFO] Validated directory: Database images
2025-05-19 14:30:27 [INFO] Validated directory: Candidate images
2025-05-19 14:30:27 [INFO] Validated directory: Fingerprint templates
2025-05-19 14:30:27 [INFO] All required files and directories validated successfully
2025-05-19 14:30:27 [INFO] Initializing application components
2025-05-19 14:30:27 [INFO] Loading hardware cache from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:30:27 [INFO] Hardware cache loaded with 0 entries
2025-05-19 14:30:27 [INFO] Loading room cache from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:30:27 [INFO] Room cache loaded with 0 entries
2025-05-19 14:30:27 [INFO] Loading seat assignments for date: 20250519
2025-05-19 14:30:27 [INFO] No seat assignments found for today (20250519)
2025-05-19 14:30:27 [INFO] Database manager initialized successfully
2025-05-19 14:30:27 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 14:30:27 [INFO] Webcam manager initialized
2025-05-19 14:30:27 [INFO] Webcam: Starting webcam...
2025-05-19 14:30:33 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 14:30:33 [INFO] Webcam started successfully
2025-05-19 14:30:33 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 14:30:33 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 14:30:33 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 14:30:33 [INFO] Post-exam mode is enabled
2025-05-19 14:30:33 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 14:30:33 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 14:30:33 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 14:30:33 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 14:30:33 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 14:30:33 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 14:30:33 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 14:30:33 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 14:30:33 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 14:30:33 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 14:30:33 [INFO] Config: Paths.dbPath = db
2025-05-19 14:30:39 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 14:30:42 [INFO] Error handler initialized
2025-05-19 14:30:42 [INFO] Read database path from config: db
2025-05-19 14:30:42 [INFO] Using database path: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 14:30:42 [INFO] Error handler initialized
2025-05-19 14:30:42 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 14:30:42 [INFO] Validating required files and directories
2025-05-19 14:30:42 [INFO] Validated directory: Database
2025-05-19 14:30:42 [INFO] Validated directory: Images
2025-05-19 14:30:42 [INFO] Validated directory: Logs
2025-05-19 14:30:42 [INFO] Validated directory: Temporary files
2025-05-19 14:30:42 [INFO] Validated directory: Database images
2025-05-19 14:30:42 [INFO] Validated directory: Candidate images
2025-05-19 14:30:42 [INFO] Validated directory: Fingerprint templates
2025-05-19 14:30:42 [INFO] All required files and directories validated successfully
2025-05-19 14:30:42 [INFO] Initializing application components
2025-05-19 14:30:42 [INFO] Loading hardware cache from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:30:42 [INFO] Hardware cache loaded with 0 entries
2025-05-19 14:30:42 [INFO] Loading room cache from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:30:42 [INFO] Room cache loaded with 0 entries
2025-05-19 14:30:42 [INFO] Loading seat assignments for date: 20250519
2025-05-19 14:30:42 [INFO] No seat assignments found for today (20250519)
2025-05-19 14:30:42 [INFO] Database manager initialized successfully
2025-05-19 14:30:42 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 14:30:42 [INFO] Webcam manager initialized
2025-05-19 14:30:42 [INFO] Webcam: Starting webcam...
2025-05-19 14:30:43 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 14:30:43 [INFO] Webcam started successfully
2025-05-19 14:30:43 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 14:30:43 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 14:30:43 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 14:30:43 [INFO] Post-exam mode is enabled
2025-05-19 14:30:43 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 14:30:43 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 14:30:43 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 14:30:43 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 14:30:43 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 14:30:43 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 14:30:43 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 14:30:43 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 14:30:43 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 14:30:43 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 14:30:43 [INFO] Config: Paths.dbPath = db
2025-05-19 14:36:24 [INFO] Application exiting: Single (Code: 0)
2025-05-19 14:36:25 [INFO] Error handler initialized
2025-05-19 14:36:25 [INFO] Read database path from config: db
2025-05-19 14:36:25 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 14:36:25 [INFO] Error handler initialized
2025-05-19 14:36:25 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 14:36:25 [INFO] Validating required files and directories
2025-05-19 14:36:25 [INFO] Validated directory: Database
2025-05-19 14:36:25 [INFO] Validated directory: Images
2025-05-19 14:36:25 [INFO] Validated directory: Logs
2025-05-19 14:36:25 [INFO] Validated directory: Temporary files
2025-05-19 14:36:25 [INFO] Validated directory: Database images
2025-05-19 14:36:25 [INFO] Validated directory: Candidate images
2025-05-19 14:36:25 [INFO] Validated directory: Fingerprint templates
2025-05-19 14:36:25 [INFO] All required files and directories validated successfully
2025-05-19 14:36:25 [INFO] Initializing application components
2025-05-19 14:36:25 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:36:25 [INFO] Hardware cache loaded with 10 entries
2025-05-19 14:36:25 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:36:25 [INFO] Room cache loaded with 3 entries
2025-05-19 14:36:25 [INFO] Loading seat assignments for date: 20250519
2025-05-19 14:36:25 [INFO] No seat assignments found for today (20250519)
2025-05-19 14:36:25 [INFO] Database manager initialized successfully
2025-05-19 14:36:25 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 14:36:25 [INFO] Webcam manager initialized
2025-05-19 14:36:25 [INFO] Webcam: Starting webcam...
2025-05-19 14:36:26 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 14:36:26 [INFO] Webcam started successfully
2025-05-19 14:36:26 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 14:36:26 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 14:36:26 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 14:36:26 [INFO] Post-exam mode is enabled
2025-05-19 14:36:26 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 14:36:26 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 14:36:26 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 14:36:26 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 14:36:26 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 14:36:27 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 14:36:27 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 14:36:27 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 14:36:27 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 14:36:27 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 14:36:27 [INFO] Config: Paths.dbPath = db
2025-05-19 14:36:27 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 14:36:27 [INFO] Added webcam feed control with default photo
2025-05-19 14:36:27 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 18:42:18 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 18:44:15 [INFO] Error handler initialized
2025-05-19 18:44:15 [INFO] Read database path from config: db
2025-05-19 18:44:15 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 18:44:15 [INFO] Error handler initialized
2025-05-19 18:44:15 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 18:44:15 [INFO] Validating required files and directories
2025-05-19 18:44:16 [INFO] Validated directory: Database
2025-05-19 18:44:16 [INFO] Validated directory: Images
2025-05-19 18:44:16 [INFO] Validated directory: Logs
2025-05-19 18:44:16 [INFO] Validated directory: Temporary files
2025-05-19 18:44:16 [INFO] Validated directory: Database images
2025-05-19 18:44:16 [INFO] Validated directory: Candidate images
2025-05-19 18:44:16 [INFO] Validated directory: Fingerprint templates
2025-05-19 18:44:16 [INFO] All required files and directories validated successfully
2025-05-19 18:44:16 [INFO] Initializing application components
2025-05-19 18:44:16 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 18:44:16 [INFO] Hardware cache loaded with 10 entries
2025-05-19 18:44:16 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 18:44:16 [INFO] Room cache loaded with 3 entries
2025-05-19 18:44:16 [INFO] Loading seat assignments for date: 20250519
2025-05-19 18:44:16 [INFO] No seat assignments found for today (20250519)
2025-05-19 18:44:16 [INFO] Database manager initialized successfully
2025-05-19 18:44:16 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 18:44:16 [INFO] Webcam manager initialized
2025-05-19 18:44:16 [INFO] Webcam: Starting webcam...
2025-05-19 18:44:17 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 18:44:17 [INFO] Webcam started successfully
2025-05-19 18:44:17 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 18:44:17 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 18:44:17 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 18:44:17 [INFO] Post-exam mode is enabled
2025-05-19 18:44:17 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 18:44:17 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 18:44:17 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 18:44:17 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 18:44:17 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 18:44:17 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 18:44:17 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 18:44:17 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 18:44:17 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 18:44:18 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 18:44:18 [INFO] Config: Paths.dbPath = db
2025-05-19 18:44:18 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 18:44:18 [INFO] Added webcam feed control with default photo
2025-05-19 18:44:18 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 18:44:39 [INFO] No seat assignment found for 9359
2025-05-19 18:45:33 [INFO] No seat assignment found for 9359
2025-05-19 18:57:00 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 19:12:14 [INFO] Error handler initialized
2025-05-19 19:12:14 [INFO] Read database path from config: db
2025-05-19 19:12:14 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 19:12:14 [INFO] Error handler initialized
2025-05-19 19:12:14 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 19:12:14 [INFO] Validating required files and directories
2025-05-19 19:12:14 [INFO] Validated directory: Database
2025-05-19 19:12:14 [INFO] Validated directory: Images
2025-05-19 19:12:14 [INFO] Validated directory: Logs
2025-05-19 19:12:14 [INFO] Validated directory: Temporary files
2025-05-19 19:12:14 [INFO] Validated directory: Database images
2025-05-19 19:12:14 [INFO] Validated directory: Candidate images
2025-05-19 19:12:14 [INFO] Validated directory: Fingerprint templates
2025-05-19 19:12:14 [INFO] All required files and directories validated successfully
2025-05-19 19:12:14 [INFO] Initializing application components
2025-05-19 19:12:14 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 19:12:14 [INFO] Hardware cache loaded with 10 entries
2025-05-19 19:12:14 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 19:12:14 [INFO] Room cache loaded with 3 entries
2025-05-19 19:12:14 [INFO] Loading seat assignments for date: 20250519
2025-05-19 19:12:14 [INFO] No seat assignments found for today (20250519)
2025-05-19 19:12:14 [INFO] Database manager initialized successfully
2025-05-19 19:12:14 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 19:12:15 [INFO] Webcam manager initialized
2025-05-19 19:12:15 [INFO] Webcam: Starting webcam...
2025-05-19 19:12:15 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 19:12:15 [INFO] Webcam started successfully
2025-05-19 19:12:15 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 19:12:15 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 19:12:15 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 19:12:15 [INFO] Post-exam mode is enabled
2025-05-19 19:12:15 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 19:12:15 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 19:12:15 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 19:12:16 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 19:12:16 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 19:12:16 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 19:12:16 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 19:12:16 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 19:12:16 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 19:12:16 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 19:12:16 [INFO] Config: Paths.dbPath = db
2025-05-19 19:12:16 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 19:12:16 [INFO] Added webcam feed control with default photo
2025-05-19 19:12:16 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 19:36:03 [INFO] Application exiting: Single (Code: 0)
2025-05-19 19:36:12 [INFO] Error handler initialized
2025-05-19 19:36:12 [INFO] Read database path from config: db
2025-05-19 19:36:12 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 19:36:12 [INFO] Error handler initialized
2025-05-19 19:36:12 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 19:36:12 [INFO] Validating required files and directories
2025-05-19 19:36:12 [INFO] Validated directory: Database
2025-05-19 19:36:13 [INFO] Validated directory: Images
2025-05-19 19:36:13 [INFO] Validated directory: Logs
2025-05-19 19:36:13 [INFO] Validated directory: Temporary files
2025-05-19 19:36:13 [INFO] Validated directory: Database images
2025-05-19 19:36:13 [INFO] Validated directory: Candidate images
2025-05-19 19:36:13 [INFO] Validated directory: Fingerprint templates
2025-05-19 19:36:13 [INFO] All required files and directories validated successfully
2025-05-19 19:36:13 [INFO] Initializing application components
2025-05-19 19:36:13 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 19:36:13 [INFO] Hardware cache loaded with 10 entries
2025-05-19 19:36:13 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 19:36:13 [INFO] Room cache loaded with 3 entries
2025-05-19 19:36:13 [INFO] Loading seat assignments for date: 20250519
2025-05-19 19:36:13 [INFO] No seat assignments found for today (20250519)
2025-05-19 19:36:13 [INFO] Database manager initialized successfully
2025-05-19 19:36:13 [INFO] Error handler initialized
2025-05-19 19:36:13 [INFO] Read database path from config: db
2025-05-19 19:36:13 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 19:36:13 [INFO] Error handler initialized
2025-05-19 19:36:13 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 19:36:13 [INFO] Validating required files and directories
2025-05-19 19:36:13 [INFO] Validated directory: Database
2025-05-19 19:36:13 [INFO] Validated directory: Images
2025-05-19 19:36:13 [INFO] Validated directory: Logs
2025-05-19 19:36:13 [INFO] Validated directory: Temporary files
2025-05-19 19:36:13 [INFO] Validated directory: Database images
2025-05-19 19:36:13 [INFO] Validated directory: Candidate images
2025-05-19 19:36:13 [INFO] Validated directory: Fingerprint templates
2025-05-19 19:36:13 [INFO] All required files and directories validated successfully
2025-05-19 19:36:13 [INFO] Initializing application components
2025-05-19 19:36:13 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 19:36:13 [INFO] Hardware cache loaded with 10 entries
2025-05-19 19:36:13 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 19:36:13 [INFO] Room cache loaded with 3 entries
2025-05-19 19:36:13 [INFO] Loading seat assignments for date: 20250519
2025-05-19 19:36:13 [INFO] No seat assignments found for today (20250519)
2025-05-19 19:36:13 [INFO] Database manager initialized successfully
2025-05-19 19:36:13 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 19:36:13 [INFO] Webcam manager initialized
2025-05-19 19:36:13 [INFO] Webcam: Starting webcam...
2025-05-19 19:36:15 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 19:36:15 [INFO] Webcam started successfully
2025-05-19 19:36:15 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 19:36:15 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 19:36:16 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 19:36:16 [INFO] Post-exam mode is enabled
2025-05-19 19:36:16 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 19:36:16 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 19:36:16 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 19:36:16 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 19:36:16 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 19:36:16 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 19:36:16 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 19:36:16 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 19:36:16 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 19:36:16 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 19:36:16 [INFO] Config: Paths.dbPath = db
2025-05-19 19:36:16 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 19:36:16 [INFO] Added webcam feed control with default photo
2025-05-19 19:36:16 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 19:36:31 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 19:36:31 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 19:36:31 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 19:36:31 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 19:36:31 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 19:36:31 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 19:36:51 [INFO] No seat assignment found for 9351
2025-05-19 19:37:01 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-19 19:37:01 [DEBUG] Room Cache: 3 entries
2025-05-19 19:37:01 [DEBUG] Hardware Cache: 10 entries
2025-05-19 19:37:01 [DEBUG] Seat Assignment Cache: 0 entries
2025-05-19 19:37:01 [INFO] Seat assigned successfully for 9351: F1-R1-S5 (Special Needs allocation)
2025-05-19 19:37:08 [INFO] Found seat assignment for 9351: F1-R1-S5
2025-05-19 19:37:41 [INFO] Found seat assignment for 9351: F1-R1-S5
2025-05-19 19:46:36 [INFO] Application exiting: Single (Code: 0)
2025-05-19 19:46:36 [INFO] Error handler initialized
2025-05-19 19:46:36 [INFO] Read database path from config: db
2025-05-19 19:46:36 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 19:46:36 [INFO] Error handler initialized
2025-05-19 19:46:36 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 19:46:36 [INFO] Validating required files and directories
2025-05-19 19:46:36 [INFO] Validated directory: Database
2025-05-19 19:46:36 [INFO] Validated directory: Images
2025-05-19 19:46:37 [INFO] Validated directory: Logs
2025-05-19 19:46:37 [INFO] Validated directory: Temporary files
2025-05-19 19:46:37 [INFO] Validated directory: Database images
2025-05-19 19:46:37 [INFO] Validated directory: Candidate images
2025-05-19 19:46:37 [INFO] Validated directory: Fingerprint templates
2025-05-19 19:46:37 [INFO] All required files and directories validated successfully
2025-05-19 19:46:37 [INFO] Initializing application components
2025-05-19 19:46:37 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 19:46:37 [INFO] Hardware cache loaded with 10 entries
2025-05-19 19:46:37 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 19:46:37 [INFO] Room cache loaded with 3 entries
2025-05-19 19:46:37 [INFO] Loading seat assignments for date: 20250519
2025-05-19 19:46:37 [INFO] Loaded 1 seat assignments for today
2025-05-19 19:46:37 [INFO] Database manager initialized successfully
2025-05-19 19:46:37 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 19:46:37 [INFO] Webcam manager initialized
2025-05-19 19:46:37 [INFO] Webcam: Starting webcam...
2025-05-19 19:46:38 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 19:46:38 [INFO] Webcam started successfully
2025-05-19 19:46:38 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 19:46:38 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 19:46:38 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 19:46:38 [INFO] Post-exam mode is enabled
2025-05-19 19:46:38 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 19:46:38 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 19:46:38 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 19:46:38 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 19:46:38 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 19:46:38 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 19:46:38 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 19:46:38 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 19:46:38 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 19:46:38 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 19:46:38 [INFO] Config: Paths.dbPath = db
2025-05-19 19:46:38 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 19:46:38 [INFO] Added webcam feed control with default photo
2025-05-19 19:46:38 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 19:46:46 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 19:48:16 [INFO] Error handler initialized
2025-05-19 19:48:16 [INFO] Read database path from config: db
2025-05-19 19:48:16 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 19:48:16 [INFO] Error handler initialized
2025-05-19 19:48:16 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 19:48:17 [INFO] Validating required files and directories
2025-05-19 19:48:17 [INFO] Validated directory: Database
2025-05-19 19:48:17 [INFO] Validated directory: Images
2025-05-19 19:48:17 [INFO] Validated directory: Logs
2025-05-19 19:48:17 [INFO] Validated directory: Temporary files
2025-05-19 19:48:17 [INFO] Validated directory: Database images
2025-05-19 19:48:17 [INFO] Validated directory: Candidate images
2025-05-19 19:48:17 [INFO] Validated directory: Fingerprint templates
2025-05-19 19:48:17 [INFO] All required files and directories validated successfully
2025-05-19 19:48:17 [INFO] Initializing application components
2025-05-19 19:48:17 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 19:48:17 [INFO] Hardware cache loaded with 10 entries
2025-05-19 19:48:17 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 19:48:17 [INFO] Room cache loaded with 3 entries
2025-05-19 19:48:17 [INFO] Loading seat assignments for date: 20250519
2025-05-19 19:48:17 [INFO] Loaded 1 seat assignments for today
2025-05-19 19:48:17 [INFO] Database manager initialized successfully
2025-05-19 19:48:17 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 19:48:17 [INFO] Webcam manager initialized
2025-05-19 19:48:17 [INFO] Webcam: Starting webcam...
2025-05-19 19:48:17 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 19:48:17 [INFO] Webcam started successfully
2025-05-19 19:48:17 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 19:48:17 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 19:48:17 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 19:48:17 [INFO] Post-exam mode is enabled
2025-05-19 19:48:17 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 19:48:17 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 19:48:17 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 19:48:17 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 19:48:17 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 19:48:17 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 19:48:17 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 19:48:17 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 19:48:17 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 19:48:17 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 19:48:18 [INFO] Config: Paths.dbPath = db
2025-05-19 19:48:18 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 19:48:18 [INFO] Added webcam feed control with default photo
2025-05-19 19:48:18 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 19:48:21 [INFO] Application exiting: Exit (Code: 0)

2025-05-23 19:23:04 [INFO] Error handler initialized
2025-05-23 19:23:04 [INFO] Read database path from config: db
2025-05-23 19:23:04 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-23 19:23:04 [INFO] Error handler initialized
2025-05-23 19:23:04 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-23 19:23:04 [INFO] PathManager initialized successfully
2025-05-23 19:23:04 [INFO] PathManager initialized successfully
2025-05-23 19:23:04 [INFO] Validating required files and directories
2025-05-23 19:23:04 [INFO] Validated directory: Database
2025-05-23 19:23:04 [INFO] Validated directory: Images
2025-05-23 19:23:04 [INFO] Validated directory: Logs
2025-05-23 19:23:04 [INFO] Validated directory: Temporary files
2025-05-23 19:23:04 [INFO] Validated directory: Candidate images
2025-05-23 19:23:04 [INFO] Validated directory: Fingerprint templates
2025-05-23 19:23:04 [INFO] All required files and directories validated successfully
2025-05-23 19:23:04 [INFO] Initializing application components
2025-05-23 19:23:04 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-23 19:23:04 [INFO] Hardware cache loaded with 10 entries
2025-05-23 19:23:04 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-23 19:23:04 [INFO] Room cache loaded with 3 entries
2025-05-23 19:23:04 [INFO] Loading seat assignments from seat ID sections
2025-05-23 19:23:04 [INFO] Loaded 0 seat assignments
2025-05-23 19:23:04 [INFO] Database manager initialized successfully
2025-05-23 19:23:04 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-23 19:23:06 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-23 19:23:06 [DEBUG] IsObject check after creation: True
2025-05-23 19:23:06 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-23 19:23:07 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-23 19:23:07 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 19:23:07 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-23 19:23:07 [DEBUG] g_fingerprintManager class handle: 51750896
2025-05-23 19:23:07 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-23 19:23:08 [INFO] Webcam started successfully
2025-05-23 19:23:08 [INFO] Config: Verification.SignatureVerification = 0
2025-05-23 19:23:08 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-23 19:23:08 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-23 19:23:08 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-23 19:23:08 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-23 19:23:08 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-23 19:23:08 [INFO] Config: Verification.FingerprintMode = save
2025-05-23 19:23:08 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-23 19:23:08 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-23 19:23:08 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-23 19:23:08 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-23 19:23:08 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-23 19:23:08 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-23 19:23:08 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-23 19:23:08 [INFO] Using webcam controls initialized in constructor
2025-05-23 19:23:08 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-23 19:23:08 [INFO] Performing one-time device status check
2025-05-23 19:23:08 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-23 19:23:08 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 19:23:08 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:23:08 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:23:08 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:23:08 [INFO] One-time device status check complete
2025-05-23 19:23:13 [INFO] Application exiting: Exit (Code: 0)
2025-05-23 19:23:13 [INFO] Unloaded avicap32.dll library
2025-05-23 19:23:16 [INFO] Error handler initialized
2025-05-23 19:23:16 [INFO] Read database path from config: db
2025-05-23 19:23:16 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-23 19:23:16 [INFO] Error handler initialized
2025-05-23 19:23:16 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-23 19:23:16 [INFO] PathManager initialized successfully
2025-05-23 19:23:16 [INFO] PathManager initialized successfully
2025-05-23 19:23:16 [INFO] Validating required files and directories
2025-05-23 19:23:16 [INFO] Validated directory: Database
2025-05-23 19:23:16 [INFO] Validated directory: Images
2025-05-23 19:23:16 [INFO] Validated directory: Logs
2025-05-23 19:23:16 [INFO] Validated directory: Temporary files
2025-05-23 19:23:16 [INFO] Validated directory: Candidate images
2025-05-23 19:23:16 [INFO] Validated directory: Fingerprint templates
2025-05-23 19:23:16 [INFO] All required files and directories validated successfully
2025-05-23 19:23:16 [INFO] Initializing application components
2025-05-23 19:23:16 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-23 19:23:16 [INFO] Hardware cache loaded with 10 entries
2025-05-23 19:23:16 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-23 19:23:16 [INFO] Room cache loaded with 3 entries
2025-05-23 19:23:16 [INFO] Loading seat assignments from seat ID sections
2025-05-23 19:23:16 [INFO] Loaded 0 seat assignments
2025-05-23 19:23:16 [INFO] Database manager initialized successfully
2025-05-23 19:23:16 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-23 19:23:18 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-23 19:23:18 [DEBUG] IsObject check after creation: True
2025-05-23 19:23:18 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-23 19:23:18 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-23 19:23:18 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 19:23:18 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-23 19:23:18 [DEBUG] g_fingerprintManager class handle: 19820704
2025-05-23 19:23:18 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-23 19:23:19 [INFO] Webcam started successfully
2025-05-23 19:23:19 [INFO] Config: Verification.SignatureVerification = 0
2025-05-23 19:23:19 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-23 19:23:19 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-23 19:23:19 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-23 19:23:19 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-23 19:23:19 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-23 19:23:19 [INFO] Config: Verification.FingerprintMode = save
2025-05-23 19:23:19 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-23 19:23:19 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-23 19:23:19 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-23 19:23:19 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-23 19:23:19 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-23 19:23:19 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-23 19:23:19 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-23 19:23:19 [INFO] Using webcam controls initialized in constructor
2025-05-23 19:23:19 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-23 19:23:20 [INFO] Performing one-time device status check
2025-05-23 19:23:20 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-23 19:23:20 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 19:23:20 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:23:20 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:23:20 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:23:20 [INFO] One-time device status check complete
2025-05-23 19:23:29 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 19:23:29 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 19:23:30 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:23:30 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:23:30 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:23:30 [INFO] No seat assignment found for 9351
2025-05-23 19:23:38 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 19:23:38 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 19:23:39 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:23:39 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:23:39 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:23:40 [INFO] No seat assignment found for 9350
2025-05-23 19:24:11 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_192357.jpg
2025-05-23 19:24:11 [INFO] Generated raw confidence value: 81
2025-05-23 19:24:11 [INFO] Stored confidence value in result object: 81
2025-05-23 19:24:11 [INFO] Photo verification PASSED with confidence 81%
2025-05-23 19:24:45 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 19:24:46 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 19:24:46 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:24:46 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:24:46 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:24:47 [INFO] No seat assignment found for 9350
2025-05-23 19:24:53 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_192449.jpg
2025-05-23 19:24:53 [INFO] Generated raw confidence value: 79
2025-05-23 19:24:53 [INFO] Stored confidence value in result object: 79
2025-05-23 19:24:53 [INFO] Photo verification PASSED with confidence 79%
2025-05-23 19:25:01 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_192458.jpg
2025-05-23 19:25:01 [INFO] Generated raw confidence value: 96
2025-05-23 19:25:01 [INFO] Stored confidence value in result object: 96
2025-05-23 19:25:01 [INFO] Photo verification PASSED with confidence 96%
2025-05-23 19:25:12 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_192509.jpg
2025-05-23 19:25:12 [INFO] Generated raw confidence value: 76
2025-05-23 19:25:12 [INFO] Stored confidence value in result object: 76
2025-05-23 19:25:12 [INFO] Photo verification PASSED with confidence 76%
2025-05-23 19:26:55 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_192651.jpg
2025-05-23 19:26:55 [INFO] Generated raw confidence value: 69
2025-05-23 19:26:55 [INFO] Stored confidence value in result object: 69
2025-05-23 19:26:55 [INFO] Photo verification FAILED with confidence 69%
2025-05-23 19:29:38 [INFO] Application exiting: Exit (Code: 0)
2025-05-23 19:29:39 [INFO] Unloaded avicap32.dll library
2025-05-23 19:30:02 [INFO] Error handler initialized
2025-05-23 19:30:02 [INFO] Read database path from config: db
2025-05-23 19:30:02 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-23 19:30:02 [INFO] Error handler initialized
2025-05-23 19:30:02 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-23 19:30:02 [INFO] PathManager initialized successfully
2025-05-23 19:30:02 [INFO] PathManager initialized successfully
2025-05-23 19:30:02 [INFO] Validating required files and directories
2025-05-23 19:30:02 [INFO] Validated directory: Database
2025-05-23 19:30:02 [INFO] Validated directory: Images
2025-05-23 19:30:02 [INFO] Validated directory: Logs
2025-05-23 19:30:02 [INFO] Validated directory: Temporary files
2025-05-23 19:30:02 [INFO] Validated directory: Candidate images
2025-05-23 19:30:02 [INFO] Validated directory: Fingerprint templates
2025-05-23 19:30:02 [INFO] All required files and directories validated successfully
2025-05-23 19:30:02 [INFO] Initializing application components
2025-05-23 19:30:02 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-23 19:30:02 [INFO] Hardware cache loaded with 10 entries
2025-05-23 19:30:02 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-23 19:30:02 [INFO] Room cache loaded with 3 entries
2025-05-23 19:30:02 [INFO] Loading seat assignments from seat ID sections
2025-05-23 19:30:02 [INFO] Loaded 0 seat assignments
2025-05-23 19:30:02 [INFO] Database manager initialized successfully
2025-05-23 19:30:02 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-23 19:30:04 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-23 19:30:04 [DEBUG] IsObject check after creation: True
2025-05-23 19:30:04 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-23 19:30:04 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-23 19:30:04 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 19:30:04 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-23 19:30:04 [DEBUG] g_fingerprintManager class handle: 51415200
2025-05-23 19:30:04 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-23 19:30:05 [INFO] Webcam started successfully
2025-05-23 19:30:05 [INFO] Config: Verification.SignatureVerification = 0
2025-05-23 19:30:05 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-23 19:30:05 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-23 19:30:05 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-23 19:30:05 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-23 19:30:05 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-23 19:30:05 [INFO] Config: Verification.FingerprintMode = save
2025-05-23 19:30:05 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-23 19:30:05 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-23 19:30:05 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-23 19:30:05 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-23 19:30:05 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-23 19:30:05 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-23 19:30:06 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-23 19:30:06 [INFO] Using webcam controls initialized in constructor
2025-05-23 19:30:06 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-23 19:30:06 [INFO] Performing one-time device status check
2025-05-23 19:30:06 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-23 19:30:06 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 19:30:06 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:30:06 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:30:06 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:30:06 [INFO] One-time device status check complete
2025-05-23 19:30:10 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 19:30:11 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 19:30:12 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:30:12 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:30:12 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:30:12 [INFO] No seat assignment found for 9350
2025-05-23 19:30:16 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_193014.jpg
2025-05-23 19:30:16 [INFO] Generated raw confidence value: 84
2025-05-23 19:30:16 [INFO] Stored confidence value in result object: 84
2025-05-23 19:30:16 [INFO] Photo verification PASSED with confidence 84%
2025-05-23 19:30:26 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_193014.jpg
2025-05-23 19:30:26 [INFO] Generated raw confidence value: 75
2025-05-23 19:30:26 [INFO] Stored confidence value in result object: 75
2025-05-23 19:30:26 [INFO] Photo verification PASSED with confidence 75%
2025-05-23 19:30:46 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_193043.jpg
2025-05-23 19:30:46 [INFO] Generated raw confidence value: 97
2025-05-23 19:30:46 [INFO] Stored confidence value in result object: 97
2025-05-23 19:30:46 [INFO] Photo verification PASSED with confidence 97%
2025-05-23 19:30:53 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_193051.jpg
2025-05-23 19:30:53 [INFO] Generated raw confidence value: 92
2025-05-23 19:30:53 [INFO] Stored confidence value in result object: 92
2025-05-23 19:30:53 [INFO] Photo verification PASSED with confidence 92%
2025-05-23 19:31:00 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_193058.jpg
2025-05-23 19:31:00 [INFO] Generated raw confidence value: 92
2025-05-23 19:31:00 [INFO] Stored confidence value in result object: 92
2025-05-23 19:31:00 [INFO] Photo verification PASSED with confidence 92%
2025-05-23 19:31:04 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_193102.jpg
2025-05-23 19:31:04 [INFO] Generated raw confidence value: 97
2025-05-23 19:31:04 [INFO] Stored confidence value in result object: 97
2025-05-23 19:31:04 [INFO] Photo verification PASSED with confidence 97%
2025-05-23 19:31:08 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_193106.jpg
2025-05-23 19:31:08 [INFO] Generated raw confidence value: 65
2025-05-23 19:31:08 [INFO] Stored confidence value in result object: 65
2025-05-23 19:31:08 [INFO] Photo verification FAILED with confidence 65%
2025-05-23 19:32:10 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 19:32:11 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 19:32:11 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:32:11 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:32:11 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:32:16 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 19:32:16 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 19:32:16 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:32:16 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:32:16 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:32:17 [INFO] No seat assignment found for 9355
2025-05-23 19:32:38 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 19:32:39 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 19:32:39 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:32:39 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:32:39 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:32:40 [INFO] No seat assignment found for 9355
2025-05-23 19:33:22 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 19:33:23 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 19:33:23 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:33:23 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:33:23 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:33:24 [INFO] No seat assignment found for 9351
2025-05-23 19:33:31 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 19:33:32 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 19:33:32 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:33:32 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:33:32 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:33:33 [INFO] No seat assignment found for 9350
2025-05-23 19:34:01 [INFO] Config: Verification.SignatureVerification = 0
2025-05-23 19:34:01 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-23 19:34:01 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-23 19:34:01 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-23 19:34:01 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-23 19:34:01 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-23 19:34:02 [INFO] Camera status updated: HD Pro Webcam C920
2025-05-23 19:34:02 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:34:02 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:34:02 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:34:05 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 19:34:06 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 19:34:06 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:34:06 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:34:06 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:34:07 [INFO] No seat assignment found for 9350
2025-05-23 19:34:24 [INFO] Fingerprint reader status updated after successful capture: Auto-detected Device
2025-05-23 19:34:24 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:34:24 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:34:24 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:34:40 [INFO] Fingerprint reader status updated after successful capture: Auto-detected Device
2025-05-23 19:34:40 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:34:40 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:34:40 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:34:47 [INFO] Fingerprint reader status updated after successful right capture: Auto-detected Device
2025-05-23 19:34:47 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:34:47 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:34:47 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:35:23 [INFO] Fingerprint reader status updated after successful capture: Auto-detected Device
2025-05-23 19:35:24 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:35:24 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:35:24 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:35:40 [INFO] Fingerprint reader status updated after successful right capture: Auto-detected Device
2025-05-23 19:35:40 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:35:40 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:35:40 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:35:49 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9350_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250523_193546.jpg
2025-05-23 19:35:49 [INFO] Generated raw confidence value: 89
2025-05-23 19:35:49 [INFO] Stored confidence value in result object: 89
2025-05-23 19:35:49 [INFO] Photo verification PASSED with confidence 89%
2025-05-23 19:36:28 [INFO] Assigning seat for candidate: 9350 (Mohit Kumar Sharma)
2025-05-23 19:36:28 [INFO] No seat assignment found for 9350
2025-05-23 19:36:28 [DEBUG] Room Cache: 3 entries
2025-05-23 19:36:28 [DEBUG] Hardware Cache: 10 entries
2025-05-23 19:36:28 [DEBUG] Seat Assignment Cache: 0 entries
2025-05-23 19:36:28 [INFO] Seat assigned successfully for 9350: F1-R1-S8 (Even Roll allocation)
2025-05-23 19:37:10 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 19:37:10 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 19:37:11 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:37:11 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:37:11 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:37:12 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-23 19:37:29 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 19:37:30 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 19:37:30 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 19:37:30 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 19:37:30 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 19:37:31 [INFO] No seat assignment found for 9351
2025-05-23 20:16:35 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 20:16:36 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 20:16:37 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:16:37 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:16:37 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:16:38 [INFO] No seat assignment found for 9351
2025-05-23 20:17:09 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 20:17:10 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 20:17:10 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:17:10 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:17:10 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:17:11 [INFO] No seat assignment found for 9351
2025-05-23 20:17:41 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 20:17:41 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 20:17:41 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:17:41 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:17:41 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:17:42 [INFO] No seat assignment found for 9353
2025-05-23 20:18:08 [INFO] Post-Exam Mode enabled by user
2025-05-23 20:18:15 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 20:18:16 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 20:18:16 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:18:16 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:18:16 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:18:17 [INFO] No seat assignment found for 9353
2025-05-23 20:18:38 [INFO] Application exiting: Exit (Code: 0)
2025-05-23 20:18:38 [INFO] Unloaded avicap32.dll library
2025-05-23 20:18:44 [INFO] Error handler initialized
2025-05-23 20:18:44 [INFO] Read database path from config: db
2025-05-23 20:18:44 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-23 20:18:44 [INFO] Error handler initialized
2025-05-23 20:18:44 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-23 20:18:44 [INFO] PathManager initialized successfully
2025-05-23 20:18:44 [INFO] PathManager initialized successfully
2025-05-23 20:18:44 [INFO] Validating required files and directories
2025-05-23 20:18:44 [INFO] Validated directory: Database
2025-05-23 20:18:44 [INFO] Validated directory: Images
2025-05-23 20:18:44 [INFO] Validated directory: Logs
2025-05-23 20:18:44 [INFO] Validated directory: Temporary files
2025-05-23 20:18:44 [INFO] Validated directory: Candidate images
2025-05-23 20:18:44 [INFO] Validated directory: Fingerprint templates
2025-05-23 20:18:44 [INFO] All required files and directories validated successfully
2025-05-23 20:18:44 [INFO] Initializing application components
2025-05-23 20:18:44 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-23 20:18:44 [INFO] Hardware cache loaded with 10 entries
2025-05-23 20:18:44 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-23 20:18:44 [INFO] Room cache loaded with 3 entries
2025-05-23 20:18:44 [INFO] Loading seat assignments from seat ID sections
2025-05-23 20:18:44 [INFO] Loaded 1 seat assignments
2025-05-23 20:18:44 [INFO] Database manager initialized successfully
2025-05-23 20:18:44 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-23 20:18:46 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-23 20:18:46 [DEBUG] IsObject check after creation: True
2025-05-23 20:18:46 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-23 20:18:47 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-23 20:18:47 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 20:18:47 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-23 20:18:47 [DEBUG] g_fingerprintManager class handle: 50390816
2025-05-23 20:18:47 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-23 20:18:48 [INFO] Webcam started successfully
2025-05-23 20:18:48 [INFO] Config: Verification.SignatureVerification = 0
2025-05-23 20:18:48 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-23 20:18:48 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-23 20:18:48 [INFO] Post-exam mode is enabled
2025-05-23 20:18:48 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-23 20:18:48 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-23 20:18:48 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-23 20:18:48 [INFO] Config: Verification.FingerprintMode = save
2025-05-23 20:18:48 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-23 20:18:48 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-23 20:18:48 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-23 20:18:48 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-23 20:18:48 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-23 20:18:48 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-23 20:18:48 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-23 20:18:48 [INFO] Using webcam controls initialized in constructor
2025-05-23 20:18:48 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-23 20:18:48 [INFO] Performing one-time device status check
2025-05-23 20:18:48 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-23 20:18:48 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 20:18:48 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:18:48 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:18:48 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:18:48 [INFO] One-time device status check complete
2025-05-23 20:18:54 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 20:18:54 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 20:18:54 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:18:54 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:18:54 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:18:55 [INFO] No seat assignment found for 9351
2025-05-23 20:19:10 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 20:19:11 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 20:19:11 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:19:11 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:19:11 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:19:12 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-23 20:19:12 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-23 20:19:12 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-23 20:19:12 [INFO] Global exam times - Start: 001200, End: 150000, Current: 201912
2025-05-23 20:19:12 [INFO] Global exam progress: 135.94594594594597% (Elapsed: 72432s, Duration: 53280s)
2025-05-23 20:19:12 [INFO] Is post-exam mode based on global timing: Yes
2025-05-23 20:19:12 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-23 20:20:03 [INFO] Post-Exam Mode disabled by user
2025-05-23 20:20:15 [INFO] Post-Exam Mode enabled by user
2025-05-23 20:21:11 [INFO] Application exiting: Exit (Code: 0)
2025-05-23 20:21:11 [INFO] Unloaded avicap32.dll library
2025-05-23 20:21:13 [INFO] Error handler initialized
2025-05-23 20:21:13 [INFO] Read database path from config: db
2025-05-23 20:21:13 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-23 20:21:13 [INFO] Error handler initialized
2025-05-23 20:21:13 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-23 20:21:13 [INFO] PathManager initialized successfully
2025-05-23 20:21:13 [INFO] PathManager initialized successfully
2025-05-23 20:21:13 [INFO] Validating required files and directories
2025-05-23 20:21:13 [INFO] Validated directory: Database
2025-05-23 20:21:13 [INFO] Validated directory: Images
2025-05-23 20:21:14 [INFO] Validated directory: Logs
2025-05-23 20:21:14 [INFO] Validated directory: Temporary files
2025-05-23 20:21:14 [INFO] Validated directory: Candidate images
2025-05-23 20:21:14 [INFO] Validated directory: Fingerprint templates
2025-05-23 20:21:14 [INFO] All required files and directories validated successfully
2025-05-23 20:21:14 [INFO] Initializing application components
2025-05-23 20:21:14 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-23 20:21:14 [INFO] Hardware cache loaded with 10 entries
2025-05-23 20:21:14 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-23 20:21:14 [INFO] Room cache loaded with 3 entries
2025-05-23 20:21:14 [INFO] Loading seat assignments from seat ID sections
2025-05-23 20:21:14 [INFO] Loaded 1 seat assignments
2025-05-23 20:21:14 [INFO] Database manager initialized successfully
2025-05-23 20:21:14 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-23 20:21:15 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-23 20:21:15 [DEBUG] IsObject check after creation: True
2025-05-23 20:21:15 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-23 20:21:16 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-23 20:21:16 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 20:21:16 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-23 20:21:16 [DEBUG] g_fingerprintManager class handle: 50402848
2025-05-23 20:21:16 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-23 20:21:17 [INFO] Webcam started successfully
2025-05-23 20:21:17 [INFO] Config: Verification.SignatureVerification = 0
2025-05-23 20:21:17 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-23 20:21:17 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-23 20:21:17 [INFO] Post-exam mode is enabled
2025-05-23 20:21:17 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-23 20:21:17 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-23 20:21:17 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-23 20:21:17 [INFO] Config: Verification.FingerprintMode = save
2025-05-23 20:21:17 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-23 20:21:17 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-23 20:21:17 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-23 20:21:17 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-23 20:21:17 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-23 20:21:17 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-23 20:21:17 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-23 20:21:17 [INFO] Using webcam controls initialized in constructor
2025-05-23 20:21:17 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-23 20:21:17 [INFO] Performing one-time device status check
2025-05-23 20:21:17 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-23 20:21:17 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 20:21:17 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:21:17 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:21:17 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:21:17 [INFO] One-time device status check complete
2025-05-23 20:21:55 [INFO] Application exiting: Exit (Code: 0)
2025-05-23 20:21:55 [INFO] Unloaded avicap32.dll library
2025-05-23 20:21:58 [INFO] Error handler initialized
2025-05-23 20:21:58 [INFO] Read database path from config: db
2025-05-23 20:21:58 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-23 20:21:58 [INFO] Error handler initialized
2025-05-23 20:21:58 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-23 20:21:58 [INFO] PathManager initialized successfully
2025-05-23 20:21:58 [INFO] PathManager initialized successfully
2025-05-23 20:21:58 [INFO] Validating required files and directories
2025-05-23 20:21:58 [INFO] Validated directory: Database
2025-05-23 20:21:58 [INFO] Validated directory: Images
2025-05-23 20:21:58 [INFO] Validated directory: Logs
2025-05-23 20:21:58 [INFO] Validated directory: Temporary files
2025-05-23 20:21:58 [INFO] Validated directory: Candidate images
2025-05-23 20:21:58 [INFO] Validated directory: Fingerprint templates
2025-05-23 20:21:58 [INFO] All required files and directories validated successfully
2025-05-23 20:21:58 [INFO] Initializing application components
2025-05-23 20:21:58 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-23 20:21:58 [INFO] Hardware cache loaded with 10 entries
2025-05-23 20:21:58 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-23 20:21:58 [INFO] Room cache loaded with 3 entries
2025-05-23 20:21:58 [INFO] Loading seat assignments from seat ID sections
2025-05-23 20:21:58 [INFO] Loaded 1 seat assignments
2025-05-23 20:21:58 [INFO] Database manager initialized successfully
2025-05-23 20:21:58 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-23 20:21:59 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-23 20:21:59 [DEBUG] IsObject check after creation: True
2025-05-23 20:21:59 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-23 20:22:00 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-23 20:22:00 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 20:22:00 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-23 20:22:00 [DEBUG] g_fingerprintManager class handle: 51517056
2025-05-23 20:22:00 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-23 20:22:01 [INFO] Webcam started successfully
2025-05-23 20:22:01 [INFO] Config: Verification.SignatureVerification = 0
2025-05-23 20:22:01 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-23 20:22:01 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-23 20:22:01 [INFO] Post-exam mode is enabled
2025-05-23 20:22:01 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-23 20:22:01 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-23 20:22:01 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-23 20:22:01 [INFO] Config: Verification.FingerprintMode = save
2025-05-23 20:22:01 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-23 20:22:01 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-23 20:22:01 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-23 20:22:01 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-23 20:22:01 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-23 20:22:01 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-23 20:22:01 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-23 20:22:01 [INFO] Using webcam controls initialized in constructor
2025-05-23 20:22:01 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-23 20:22:01 [INFO] Performing one-time device status check
2025-05-23 20:22:01 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-23 20:22:01 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 20:22:01 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:22:01 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:22:01 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:22:02 [INFO] One-time device status check complete
2025-05-23 20:22:21 [INFO] Application exiting: Exit (Code: 0)
2025-05-23 20:22:21 [INFO] Unloaded avicap32.dll library
2025-05-23 20:22:24 [INFO] Error handler initialized
2025-05-23 20:22:24 [INFO] Read database path from config: db
2025-05-23 20:22:24 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-23 20:22:24 [INFO] Error handler initialized
2025-05-23 20:22:24 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-23 20:22:24 [INFO] PathManager initialized successfully
2025-05-23 20:22:24 [INFO] PathManager initialized successfully
2025-05-23 20:22:24 [INFO] Validating required files and directories
2025-05-23 20:22:24 [INFO] Validated directory: Database
2025-05-23 20:22:24 [INFO] Validated directory: Images
2025-05-23 20:22:24 [INFO] Validated directory: Logs
2025-05-23 20:22:24 [INFO] Validated directory: Temporary files
2025-05-23 20:22:24 [INFO] Validated directory: Candidate images
2025-05-23 20:22:24 [INFO] Validated directory: Fingerprint templates
2025-05-23 20:22:24 [INFO] All required files and directories validated successfully
2025-05-23 20:22:24 [INFO] Initializing application components
2025-05-23 20:22:24 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-23 20:22:24 [INFO] Hardware cache loaded with 10 entries
2025-05-23 20:22:24 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-23 20:22:24 [INFO] Room cache loaded with 3 entries
2025-05-23 20:22:24 [INFO] Loading seat assignments from seat ID sections
2025-05-23 20:22:24 [INFO] Loaded 1 seat assignments
2025-05-23 20:22:25 [INFO] Database manager initialized successfully
2025-05-23 20:22:25 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-23 20:22:26 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-23 20:22:26 [DEBUG] IsObject check after creation: True
2025-05-23 20:22:26 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-23 20:22:27 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-23 20:22:27 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 20:22:27 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-23 20:22:27 [DEBUG] g_fingerprintManager class handle: 25586592
2025-05-23 20:22:27 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-23 20:22:27 [INFO] Webcam started successfully
2025-05-23 20:22:27 [INFO] Config: Verification.SignatureVerification = 0
2025-05-23 20:22:27 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-23 20:22:27 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-23 20:22:27 [INFO] Post-exam mode is enabled
2025-05-23 20:22:27 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-23 20:22:27 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-23 20:22:28 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-23 20:22:28 [INFO] Config: Verification.FingerprintMode = save
2025-05-23 20:22:28 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-23 20:22:28 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-23 20:22:28 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-23 20:22:28 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-23 20:22:28 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-23 20:22:28 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-23 20:22:28 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-23 20:22:28 [INFO] Using webcam controls initialized in constructor
2025-05-23 20:22:28 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-23 20:22:28 [INFO] Performing one-time device status check
2025-05-23 20:22:28 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-23 20:22:28 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 20:22:28 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:22:28 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:22:28 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:22:28 [INFO] One-time device status check complete
2025-05-23 20:22:31 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 20:22:31 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 20:22:31 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:22:31 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:22:31 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:22:32 [INFO] No seat assignment found for 9351
2025-05-23 20:22:38 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 20:22:39 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 20:22:39 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:22:39 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:22:39 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:22:40 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-23 20:22:40 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-23 20:22:40 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-23 20:22:40 [INFO] Global exam times - Start: 001200, End: 150000, Current: 202240
2025-05-23 20:22:40 [INFO] Global exam progress: 136.33633633633633% (Elapsed: 72640s, Duration: 53280s)
2025-05-23 20:22:40 [INFO] Is post-exam mode based on global timing: Yes
2025-05-23 20:22:40 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-23 20:22:55 [INFO] Application exiting: Exit (Code: 0)
2025-05-23 20:22:55 [INFO] Unloaded avicap32.dll library
2025-05-23 20:22:57 [INFO] Error handler initialized
2025-05-23 20:22:57 [INFO] Read database path from config: db
2025-05-23 20:22:57 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-23 20:22:57 [INFO] Error handler initialized
2025-05-23 20:22:57 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-23 20:22:57 [INFO] PathManager initialized successfully
2025-05-23 20:22:57 [INFO] PathManager initialized successfully
2025-05-23 20:22:57 [INFO] Validating required files and directories
2025-05-23 20:22:57 [INFO] Validated directory: Database
2025-05-23 20:22:57 [INFO] Validated directory: Images
2025-05-23 20:22:57 [INFO] Validated directory: Logs
2025-05-23 20:22:57 [INFO] Validated directory: Temporary files
2025-05-23 20:22:57 [INFO] Validated directory: Candidate images
2025-05-23 20:22:57 [INFO] Validated directory: Fingerprint templates
2025-05-23 20:22:57 [INFO] All required files and directories validated successfully
2025-05-23 20:22:57 [INFO] Initializing application components
2025-05-23 20:22:57 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-23 20:22:57 [INFO] Hardware cache loaded with 10 entries
2025-05-23 20:22:57 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-23 20:22:57 [INFO] Room cache loaded with 3 entries
2025-05-23 20:22:57 [INFO] Loading seat assignments from seat ID sections
2025-05-23 20:22:57 [INFO] Loaded 1 seat assignments
2025-05-23 20:22:57 [INFO] Database manager initialized successfully
2025-05-23 20:22:57 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-23 20:22:59 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-23 20:22:59 [DEBUG] IsObject check after creation: True
2025-05-23 20:22:59 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-23 20:22:59 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-23 20:22:59 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 20:22:59 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-23 20:22:59 [DEBUG] g_fingerprintManager class handle: 10488432
2025-05-23 20:23:00 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-23 20:23:00 [INFO] Webcam started successfully
2025-05-23 20:23:00 [INFO] Config: Verification.SignatureVerification = 0
2025-05-23 20:23:00 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-23 20:23:00 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-23 20:23:00 [INFO] Post-exam mode is enabled
2025-05-23 20:23:00 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-23 20:23:00 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-23 20:23:00 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-23 20:23:00 [INFO] Config: Verification.FingerprintMode = save
2025-05-23 20:23:00 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-23 20:23:00 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-23 20:23:00 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-23 20:23:00 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-23 20:23:00 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-23 20:23:00 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-23 20:23:00 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-23 20:23:00 [INFO] Using webcam controls initialized in constructor
2025-05-23 20:23:00 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-23 20:23:01 [INFO] Performing one-time device status check
2025-05-23 20:23:01 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-23 20:23:01 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 20:23:01 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:23:01 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:23:01 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:23:01 [INFO] One-time device status check complete
2025-05-23 20:23:06 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 20:23:07 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 20:23:07 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:23:07 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:23:07 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:23:08 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-23 20:23:08 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-23 20:23:08 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-23 20:23:08 [INFO] Global exam times - Start: 001200, End: 150000, Current: 202308
2025-05-23 20:23:08 [INFO] Global exam progress: 136.38888888888889% (Elapsed: 72668s, Duration: 53280s)
2025-05-23 20:23:08 [INFO] Is post-exam mode based on global timing: Yes
2025-05-23 20:23:08 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-23 20:23:26 [INFO] Application exiting: Exit (Code: 0)
2025-05-23 20:23:26 [INFO] Unloaded avicap32.dll library
2025-05-23 20:23:33 [INFO] Error handler initialized
2025-05-23 20:23:33 [INFO] Read database path from config: db
2025-05-23 20:23:33 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-23 20:23:33 [INFO] Error handler initialized
2025-05-23 20:23:33 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-23 20:23:33 [INFO] PathManager initialized successfully
2025-05-23 20:23:33 [INFO] PathManager initialized successfully
2025-05-23 20:23:33 [INFO] Validating required files and directories
2025-05-23 20:23:33 [INFO] Validated directory: Database
2025-05-23 20:23:33 [INFO] Validated directory: Images
2025-05-23 20:23:33 [INFO] Validated directory: Logs
2025-05-23 20:23:33 [INFO] Validated directory: Temporary files
2025-05-23 20:23:33 [INFO] Validated directory: Candidate images
2025-05-23 20:23:33 [INFO] Validated directory: Fingerprint templates
2025-05-23 20:23:33 [INFO] All required files and directories validated successfully
2025-05-23 20:23:33 [INFO] Initializing application components
2025-05-23 20:23:33 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-23 20:23:33 [INFO] Hardware cache loaded with 10 entries
2025-05-23 20:23:33 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-23 20:23:33 [INFO] Room cache loaded with 3 entries
2025-05-23 20:23:33 [INFO] Loading seat assignments from seat ID sections
2025-05-23 20:23:33 [INFO] Loaded 1 seat assignments
2025-05-23 20:23:33 [INFO] Database manager initialized successfully
2025-05-23 20:23:33 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-23 20:23:35 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-23 20:23:35 [DEBUG] IsObject check after creation: True
2025-05-23 20:23:35 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-23 20:23:35 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-23 20:23:35 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 20:23:35 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-23 20:23:35 [DEBUG] g_fingerprintManager class handle: 50464320
2025-05-23 20:23:35 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-23 20:23:36 [INFO] Webcam started successfully
2025-05-23 20:23:36 [INFO] Config: Verification.SignatureVerification = 0
2025-05-23 20:23:36 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-23 20:23:36 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-23 20:23:36 [INFO] Post-exam mode is enabled
2025-05-23 20:23:36 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-23 20:23:36 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-23 20:23:36 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-23 20:23:36 [INFO] Config: Verification.FingerprintMode = save
2025-05-23 20:23:36 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-23 20:23:36 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-23 20:23:36 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-23 20:23:36 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-23 20:23:36 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-23 20:23:36 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-23 20:23:36 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-23 20:23:36 [INFO] Using webcam controls initialized in constructor
2025-05-23 20:23:36 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-23 20:23:37 [INFO] Performing one-time device status check
2025-05-23 20:23:37 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-23 20:23:37 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-23 20:23:37 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:23:37 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:23:37 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:23:37 [INFO] One-time device status check complete
2025-05-23 20:23:40 [INFO] Camera stopped and status updated: Not Connected
2025-05-23 20:23:41 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-23 20:23:41 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-23 20:23:41 [DEBUG] Using serial number in footer: H58230901549
2025-05-23 20:23:41 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-23 20:23:42 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-23 20:23:42 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-23 20:23:42 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-23 20:23:42 [INFO] Global exam times - Start: 001200, End: 150000, Current: 202342
2025-05-23 20:23:42 [INFO] Global exam progress: 136.45270270270271% (Elapsed: 72702s, Duration: 53280s)
2025-05-23 20:23:42 [INFO] Is post-exam mode based on global timing: Yes
2025-05-23 20:23:42 [INFO] Entering post-exam verification mode for candidate: 9350

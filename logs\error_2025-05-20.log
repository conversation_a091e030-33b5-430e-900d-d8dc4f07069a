=== WinCBT-Biometric Error Log ===
Started: 2025-05-20 06:34:50
----------------------------------------
2025-05-20 06:34:51 [INFO] Error handler initialized
2025-05-20 06:34:51 [INFO] Read database path from config: db
2025-05-20 06:34:51 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 06:34:51 [INFO] Error handler initialized
2025-05-20 06:34:51 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 06:34:51 [INFO] PathManager initialized successfully
2025-05-20 06:34:51 [INFO] PathManager initialized successfully
2025-05-20 06:34:51 [INFO] Validating required files and directories
2025-05-20 06:34:51 [INFO] Validated directory: Database
2025-05-20 06:34:51 [INFO] Validated directory: Images
2025-05-20 06:34:51 [INFO] Validated directory: Logs
2025-05-20 06:34:51 [INFO] Validated directory: Temporary files
2025-05-20 06:34:51 [INFO] Validated directory: Candidate images
2025-05-20 06:34:51 [INFO] Validated directory: Fingerprint templates
2025-05-20 06:34:51 [INFO] All required files and directories validated successfully
2025-05-20 06:34:51 [INFO] Initializing application components
2025-05-20 06:34:51 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 06:34:51 [INFO] Hardware cache loaded with 10 entries
2025-05-20 06:34:51 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 06:34:51 [INFO] Room cache loaded with 3 entries
2025-05-20 06:34:51 [INFO] Loading seat assignments for date: 20250520
2025-05-20 06:34:51 [INFO] No seat assignments found for today (20250520)
2025-05-20 06:34:51 [INFO] Database manager initialized successfully
2025-05-20 06:34:51 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 06:34:51 [INFO] Webcam manager initialized
2025-05-20 06:34:51 [INFO] Webcam: Starting webcam...
2025-05-20 06:34:52 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 06:34:52 [INFO] Webcam started successfully
2025-05-20 06:34:52 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 06:34:52 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 06:34:52 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 06:34:52 [INFO] Post-exam mode is enabled
2025-05-20 06:34:52 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 06:34:52 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 06:34:52 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 06:34:52 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 06:34:52 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 06:34:52 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 06:34:52 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 06:34:52 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 06:34:52 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 06:34:52 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 06:34:52 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 06:34:52 [INFO] Added webcam feed control with default photo
2025-05-20 06:34:53 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 06:35:04 [INFO] No seat assignment found for 9353
2025-05-20 06:35:15 [INFO] Assigning seat for candidate: 9353 (Arjun Singh)
2025-05-20 06:35:15 [DEBUG] Room Cache: 3 entries
2025-05-20 06:35:15 [DEBUG] Hardware Cache: 10 entries
2025-05-20 06:35:15 [DEBUG] Seat Assignment Cache: 0 entries
2025-05-20 06:35:15 [INFO] Seat assigned successfully for 9353: F1-R1-S8 (Odd Roll allocation)
2025-05-20 06:36:01 [INFO] No seat assignment found for 9359
2025-05-20 06:36:09 [INFO] Webcam: Webcam stopped
2025-05-20 06:36:09 [INFO] Webcam: Capturing image via FFmpeg from camera: HD Pro Webcam C920
2025-05-20 06:36:14 [INFO] Webcam: Image captured successfully: C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250520_063609.jpg
2025-05-20 06:36:17 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9359_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250520_063609.jpg
2025-05-20 06:36:17 [ERROR] Registered photo not found: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9359_photo.jpg
2025-05-20 06:37:03 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9359_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250520_063609.jpg
2025-05-20 06:37:03 [INFO] Photo verification PASSED with confidence 95%
2025-05-20 06:37:29 [INFO] No seat assignment found for 9357
2025-05-20 06:39:19 [INFO] No seat assignment found for 9359
2025-05-20 06:39:24 [INFO] Assigning seat for candidate: 9359 (Aman Mehra)
2025-05-20 06:39:24 [DEBUG] Room Cache: 3 entries
2025-05-20 06:39:24 [DEBUG] Hardware Cache: 10 entries
2025-05-20 06:39:24 [DEBUG] Seat Assignment Cache: 1 entries
2025-05-20 06:39:24 [INFO] Seat assigned successfully for 9359: F1-R1-S10 (Special Needs allocation)
2025-05-20 06:47:07 [INFO] Error handler initialized
2025-05-20 06:47:07 [INFO] Read database path from config: db
2025-05-20 06:47:07 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 06:47:07 [INFO] Error handler initialized
2025-05-20 06:47:07 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 06:47:07 [INFO] PathManager initialized successfully
2025-05-20 06:47:07 [INFO] PathManager initialized successfully
2025-05-20 06:47:07 [INFO] Validating required files and directories
2025-05-20 06:47:07 [INFO] Validated directory: Database
2025-05-20 06:47:07 [INFO] Validated directory: Images
2025-05-20 06:47:07 [INFO] Validated directory: Logs
2025-05-20 06:47:07 [INFO] Validated directory: Temporary files
2025-05-20 06:47:07 [INFO] Validated directory: Candidate images
2025-05-20 06:47:07 [INFO] Validated directory: Fingerprint templates
2025-05-20 06:47:07 [INFO] All required files and directories validated successfully
2025-05-20 06:47:08 [INFO] Initializing application components
2025-05-20 06:47:08 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 06:47:08 [INFO] Hardware cache loaded with 10 entries
2025-05-20 06:47:08 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 06:47:08 [INFO] Room cache loaded with 3 entries
2025-05-20 06:47:08 [INFO] Loading seat assignments for date: 20250520
2025-05-20 06:47:08 [INFO] No seat assignments found for today (20250520)
2025-05-20 06:47:08 [INFO] Database manager initialized successfully
2025-05-20 06:47:08 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 06:47:08 [INFO] Webcam manager initialized
2025-05-20 06:47:08 [INFO] Webcam: Starting webcam...
2025-05-20 06:47:09 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 06:47:09 [INFO] Webcam started successfully
2025-05-20 06:47:09 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 06:47:09 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 06:47:09 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 06:47:09 [INFO] Post-exam mode is enabled
2025-05-20 06:47:09 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 06:47:09 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 06:47:09 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 06:47:09 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 06:47:09 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 06:47:09 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 06:47:09 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 06:47:09 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 06:47:09 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 06:47:09 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 06:47:09 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 06:47:09 [INFO] Added webcam feed control with default photo
2025-05-20 06:47:09 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 06:47:14 [INFO] No seat assignment found for 9351
2025-05-20 06:47:19 [INFO] Webcam: Webcam stopped
2025-05-20 06:47:19 [INFO] Webcam: Capturing image via FFmpeg from camera: HD Pro Webcam C920
2025-05-20 06:47:21 [INFO] Webcam: Image captured successfully: C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250520_064719.jpg
2025-05-20 06:47:23 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9351_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250520_064719.jpg
2025-05-20 06:47:23 [ERROR] Registered photo not found: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9351_photo.jpg
2025-05-20 06:47:24 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9351_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250520_064719.jpg
2025-05-20 06:47:24 [INFO] Photo verification PASSED with confidence 96%
2025-05-20 06:47:37 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 06:47:37 [DEBUG] Room Cache: 3 entries
2025-05-20 06:47:37 [DEBUG] Hardware Cache: 10 entries
2025-05-20 06:47:37 [DEBUG] Seat Assignment Cache: 0 entries
2025-05-20 06:47:37 [INFO] Seat assigned successfully for 9351: F1-R1-S1 (Special Needs allocation)
2025-05-20 06:49:28 [INFO] Application exiting: Single (Code: 0)
2025-05-20 06:49:28 [INFO] Error handler initialized
2025-05-20 06:49:28 [INFO] Read database path from config: db
2025-05-20 06:49:28 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 06:49:28 [INFO] Error handler initialized
2025-05-20 06:49:28 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 06:49:28 [INFO] PathManager initialized successfully
2025-05-20 06:49:28 [INFO] PathManager initialized successfully
2025-05-20 06:49:28 [INFO] Validating required files and directories
2025-05-20 06:49:28 [INFO] Validated directory: Database
2025-05-20 06:49:28 [INFO] Validated directory: Images
2025-05-20 06:49:28 [INFO] Validated directory: Logs
2025-05-20 06:49:28 [INFO] Validated directory: Temporary files
2025-05-20 06:49:28 [INFO] Validated directory: Candidate images
2025-05-20 06:49:28 [INFO] Validated directory: Fingerprint templates
2025-05-20 06:49:28 [INFO] All required files and directories validated successfully
2025-05-20 06:49:28 [INFO] Initializing application components
2025-05-20 06:49:28 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 06:49:28 [INFO] Hardware cache loaded with 10 entries
2025-05-20 06:49:28 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 06:49:28 [INFO] Room cache loaded with 3 entries
2025-05-20 06:49:28 [INFO] Loading seat assignments for date: 20250520
2025-05-20 06:49:29 [INFO] Loaded 1 seat assignments for today
2025-05-20 06:49:29 [INFO] Database manager initialized successfully
2025-05-20 06:49:29 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 06:49:29 [INFO] Webcam manager initialized
2025-05-20 06:49:29 [INFO] Webcam: Starting webcam...
2025-05-20 06:49:29 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 06:49:29 [INFO] Webcam started successfully
2025-05-20 06:49:29 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 06:49:29 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 06:49:29 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 06:49:29 [INFO] Post-exam mode is enabled
2025-05-20 06:49:29 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 06:49:29 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 06:49:29 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 06:49:29 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 06:49:29 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 06:49:29 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 06:49:29 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 06:49:29 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 06:49:29 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 06:49:29 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 06:49:29 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 06:49:29 [INFO] Added webcam feed control with default photo
2025-05-20 06:49:29 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 06:49:33 [INFO] Found seat assignment for 9351: F1-R1-S1
2025-05-20 06:49:42 [INFO] No seat assignment found for 9353
2025-05-20 06:50:09 [INFO] No seat assignment found for 9351
2025-05-20 06:50:13 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 06:50:13 [DEBUG] Room Cache: 3 entries
2025-05-20 06:50:13 [DEBUG] Hardware Cache: 10 entries
2025-05-20 06:50:13 [DEBUG] Seat Assignment Cache: 1 entries
2025-05-20 06:50:13 [INFO] Seat assigned successfully for 9351: F1-R1-S2 (Special Needs allocation)
2025-05-20 06:51:39 [INFO] Found seat assignment for 9351: F1-R1-S2
2025-05-20 06:52:05 [INFO] Application exiting: Single (Code: 0)
2025-05-20 06:52:05 [INFO] Error handler initialized
2025-05-20 06:52:05 [INFO] Read database path from config: db
2025-05-20 06:52:05 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 06:52:05 [INFO] Error handler initialized
2025-05-20 06:52:05 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 06:52:05 [INFO] PathManager initialized successfully
2025-05-20 06:52:05 [INFO] PathManager initialized successfully
2025-05-20 06:52:05 [INFO] Validating required files and directories
2025-05-20 06:52:05 [INFO] Validated directory: Database
2025-05-20 06:52:05 [INFO] Validated directory: Images
2025-05-20 06:52:05 [INFO] Validated directory: Logs
2025-05-20 06:52:05 [INFO] Validated directory: Temporary files
2025-05-20 06:52:05 [INFO] Validated directory: Candidate images
2025-05-20 06:52:05 [INFO] Validated directory: Fingerprint templates
2025-05-20 06:52:05 [INFO] All required files and directories validated successfully
2025-05-20 06:52:05 [INFO] Initializing application components
2025-05-20 06:52:05 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 06:52:05 [INFO] Hardware cache loaded with 10 entries
2025-05-20 06:52:05 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 06:52:05 [INFO] Room cache loaded with 3 entries
2025-05-20 06:52:05 [INFO] Loading seat assignments for date: 20250520
2025-05-20 06:52:05 [INFO] No seat assignments found for today (20250520)
2025-05-20 06:52:05 [INFO] Database manager initialized successfully
2025-05-20 06:52:05 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 06:52:05 [INFO] Webcam manager initialized
2025-05-20 06:52:05 [INFO] Webcam: Starting webcam...
2025-05-20 06:52:06 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 06:52:06 [INFO] Webcam started successfully
2025-05-20 06:52:06 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 06:52:06 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 06:52:06 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 06:52:06 [INFO] Post-exam mode is enabled
2025-05-20 06:52:06 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 06:52:06 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 06:52:06 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 06:52:06 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 06:52:06 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 06:52:06 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 06:52:06 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 06:52:06 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 06:52:06 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 06:52:06 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 06:52:06 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 06:52:06 [INFO] Added webcam feed control with default photo
2025-05-20 06:52:06 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 06:52:09 [INFO] No seat assignment found for 9351
2025-05-20 06:52:14 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 06:52:14 [DEBUG] Room Cache: 3 entries
2025-05-20 06:52:14 [DEBUG] Hardware Cache: 10 entries
2025-05-20 06:52:14 [DEBUG] Seat Assignment Cache: 0 entries
2025-05-20 06:52:14 [INFO] Seat assigned successfully for 9351: F1-R1-S7 (Special Needs allocation)
2025-05-20 06:58:29 [INFO] Application exiting: Single (Code: 0)
2025-05-20 06:58:30 [INFO] Error handler initialized
2025-05-20 06:58:30 [INFO] Read database path from config: db
2025-05-20 06:58:30 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 06:58:30 [INFO] Error handler initialized
2025-05-20 06:58:30 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 06:58:30 [INFO] PathManager initialized successfully
2025-05-20 06:58:30 [INFO] PathManager initialized successfully
2025-05-20 06:58:30 [INFO] Validating required files and directories
2025-05-20 06:58:30 [INFO] Validated directory: Database
2025-05-20 06:58:30 [INFO] Validated directory: Images
2025-05-20 06:58:30 [INFO] Validated directory: Logs
2025-05-20 06:58:30 [INFO] Validated directory: Temporary files
2025-05-20 06:58:30 [INFO] Validated directory: Candidate images
2025-05-20 06:58:30 [INFO] Validated directory: Fingerprint templates
2025-05-20 06:58:30 [INFO] All required files and directories validated successfully
2025-05-20 06:58:30 [INFO] Initializing application components
2025-05-20 06:58:30 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 06:58:30 [INFO] Hardware cache loaded with 10 entries
2025-05-20 06:58:30 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 06:58:30 [INFO] Room cache loaded with 3 entries
2025-05-20 06:58:30 [INFO] Loading seat assignments for date: 20250520
2025-05-20 06:58:30 [INFO] Loaded 1 seat assignments for today
2025-05-20 06:58:30 [INFO] Database manager initialized successfully
2025-05-20 06:58:30 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 06:58:30 [INFO] Webcam manager initialized
2025-05-20 06:58:30 [INFO] Webcam: Starting webcam...
2025-05-20 06:58:31 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 06:58:31 [INFO] Webcam started successfully
2025-05-20 06:58:31 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 06:58:31 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 06:58:31 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 06:58:31 [INFO] Post-exam mode is enabled
2025-05-20 06:58:31 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 06:58:31 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 06:58:31 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 06:58:31 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 06:58:31 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 06:58:31 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 06:58:31 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 06:58:31 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 06:58:31 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 06:58:31 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 06:58:31 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 06:58:31 [INFO] Added webcam feed control with default photo
2025-05-20 06:58:31 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 06:58:49 [INFO] No seat assignment found for 9351
2025-05-20 06:59:01 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 06:59:01 [DEBUG] Room Cache: 3 entries
2025-05-20 06:59:01 [DEBUG] Hardware Cache: 10 entries
2025-05-20 06:59:02 [DEBUG] Seat Assignment Cache: 1 entries
2025-05-20 06:59:02 [INFO] Seat assigned successfully for 9351: F1-R1-S8 (Special Needs allocation)
2025-05-20 07:00:58 [INFO] No seat assignment found for 9351
2025-05-20 07:01:00 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 07:01:00 [DEBUG] Room Cache: 3 entries
2025-05-20 07:01:00 [DEBUG] Hardware Cache: 10 entries
2025-05-20 07:01:00 [DEBUG] Seat Assignment Cache: 2 entries
2025-05-20 07:01:01 [INFO] Seat assigned successfully for 9351: F1-R1-S5 (Special Needs allocation)
2025-05-20 07:03:13 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 07:05:20 [INFO] Error handler initialized
2025-05-20 07:05:20 [INFO] Read database path from config: db
2025-05-20 07:05:20 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:05:20 [INFO] Error handler initialized
2025-05-20 07:05:20 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 07:05:20 [INFO] PathManager initialized successfully
2025-05-20 07:05:20 [INFO] PathManager initialized successfully
2025-05-20 07:05:20 [INFO] Validating required files and directories
2025-05-20 07:05:20 [INFO] Validated directory: Database
2025-05-20 07:05:20 [INFO] Validated directory: Images
2025-05-20 07:05:20 [INFO] Validated directory: Logs
2025-05-20 07:05:20 [INFO] Validated directory: Temporary files
2025-05-20 07:05:20 [INFO] Validated directory: Candidate images
2025-05-20 07:05:20 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:05:20 [INFO] All required files and directories validated successfully
2025-05-20 07:05:20 [INFO] Initializing application components
2025-05-20 07:05:20 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:05:20 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:05:20 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:05:20 [INFO] Room cache loaded with 3 entries
2025-05-20 07:05:20 [INFO] Loading seat assignments for date: 20250520
2025-05-20 07:05:20 [INFO] No seat assignments found for today (20250520)
2025-05-20 07:05:20 [INFO] Database manager initialized successfully
2025-05-20 07:05:20 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:05:20 [INFO] Webcam manager initialized
2025-05-20 07:05:20 [INFO] Webcam: Starting webcam...
2025-05-20 07:05:21 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:05:21 [INFO] Webcam started successfully
2025-05-20 07:05:21 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:05:21 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:05:21 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:05:21 [INFO] Post-exam mode is enabled
2025-05-20 07:05:21 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:05:21 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:05:21 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:05:21 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:05:21 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:05:21 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:05:21 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:05:21 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:05:21 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:05:21 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:05:21 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:05:21 [INFO] Added webcam feed control with default photo
2025-05-20 07:05:22 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:05:25 [INFO] No seat assignment found for 9351
2025-05-20 07:05:28 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 07:05:28 [DEBUG] Room Cache: 3 entries
2025-05-20 07:05:28 [DEBUG] Hardware Cache: 10 entries
2025-05-20 07:05:28 [DEBUG] Seat Assignment Cache: 0 entries
2025-05-20 07:05:28 [INFO] Seat assigned successfully for 9351: F1-R1-S2 (Special Needs allocation)
2025-05-20 07:08:28 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 07:12:42 [INFO] Error handler initialized
2025-05-20 07:12:42 [INFO] Read database path from config: db
2025-05-20 07:12:42 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:12:42 [INFO] Error handler initialized
2025-05-20 07:12:42 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 07:12:42 [INFO] PathManager initialized successfully
2025-05-20 07:12:42 [INFO] PathManager initialized successfully
2025-05-20 07:12:42 [INFO] Validating required files and directories
2025-05-20 07:12:42 [INFO] Validated directory: Database
2025-05-20 07:12:42 [INFO] Validated directory: Images
2025-05-20 07:12:42 [INFO] Validated directory: Logs
2025-05-20 07:12:42 [INFO] Validated directory: Temporary files
2025-05-20 07:12:42 [INFO] Validated directory: Candidate images
2025-05-20 07:12:42 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:12:42 [INFO] All required files and directories validated successfully
2025-05-20 07:12:42 [INFO] Initializing application components
2025-05-20 07:12:42 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:12:42 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:12:42 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:12:42 [INFO] Room cache loaded with 3 entries
2025-05-20 07:12:42 [INFO] Loading seat assignments for date: 20250520
2025-05-20 07:12:42 [INFO] Loaded 1 seat assignments for today
2025-05-20 07:12:42 [INFO] Database manager initialized successfully
2025-05-20 07:12:42 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:12:42 [INFO] Webcam manager initialized
2025-05-20 07:12:42 [INFO] Webcam: Starting webcam...
2025-05-20 07:12:43 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:12:43 [INFO] Webcam started successfully
2025-05-20 07:12:43 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:12:43 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:12:43 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:12:43 [INFO] Post-exam mode is enabled
2025-05-20 07:12:43 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:12:43 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:12:43 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:12:43 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:12:43 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:12:43 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:12:43 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:12:43 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:12:43 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:12:43 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:12:43 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:12:43 [INFO] Added webcam feed control with default photo
2025-05-20 07:12:44 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:13:23 [INFO] Error handler initialized
2025-05-20 07:13:23 [INFO] Read database path from config: db
2025-05-20 07:13:23 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:13:23 [INFO] Error handler initialized
2025-05-20 07:13:23 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 07:13:23 [INFO] PathManager initialized successfully
2025-05-20 07:13:23 [INFO] PathManager initialized successfully
2025-05-20 07:13:23 [INFO] Validating required files and directories
2025-05-20 07:13:23 [INFO] Validated directory: Database
2025-05-20 07:13:23 [INFO] Validated directory: Images
2025-05-20 07:13:23 [INFO] Validated directory: Logs
2025-05-20 07:13:23 [INFO] Validated directory: Temporary files
2025-05-20 07:13:23 [INFO] Validated directory: Candidate images
2025-05-20 07:13:23 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:13:23 [INFO] All required files and directories validated successfully
2025-05-20 07:13:23 [INFO] Initializing application components
2025-05-20 07:13:23 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:13:23 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:13:23 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:13:23 [INFO] Room cache loaded with 3 entries
2025-05-20 07:13:23 [INFO] Loading seat assignments for date: 20250520
2025-05-20 07:13:23 [INFO] No seat assignments found for today (20250520)
2025-05-20 07:13:23 [INFO] Database manager initialized successfully
2025-05-20 07:13:23 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:13:23 [INFO] Webcam manager initialized
2025-05-20 07:13:23 [INFO] Webcam: Starting webcam...
2025-05-20 07:13:24 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:13:24 [INFO] Webcam started successfully
2025-05-20 07:13:24 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:13:24 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:13:24 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:13:24 [INFO] Post-exam mode is enabled
2025-05-20 07:13:24 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:13:24 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:13:24 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:13:24 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:13:24 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:13:24 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:13:24 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:13:24 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:13:24 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:13:24 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:13:24 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:13:24 [INFO] Added webcam feed control with default photo
2025-05-20 07:13:24 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:13:27 [INFO] No seat assignment found for 9351
2025-05-20 07:13:30 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 07:13:30 [DEBUG] Room Cache: 3 entries
2025-05-20 07:13:30 [DEBUG] Hardware Cache: 10 entries
2025-05-20 07:13:30 [DEBUG] Seat Assignment Cache: 0 entries
2025-05-20 07:13:30 [INFO] Seat assigned successfully for 9351: F1-R1-S1 (Special Needs allocation)
2025-05-20 07:13:46 [INFO] Found seat assignment for 9351: F1-R1-S1
2025-05-20 07:13:56 [INFO] Found seat assignment for 9351: F1-R1-S1
2025-05-20 07:14:06 [INFO] No seat assignment found for 9353
2025-05-20 07:15:06 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 07:16:55 [INFO] Error handler initialized
2025-05-20 07:16:55 [INFO] Read database path from config: db
2025-05-20 07:16:55 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:16:55 [INFO] Error handler initialized
2025-05-20 07:16:55 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 07:16:55 [INFO] PathManager initialized successfully
2025-05-20 07:16:55 [INFO] PathManager initialized successfully
2025-05-20 07:16:55 [INFO] Validating required files and directories
2025-05-20 07:16:55 [INFO] Validated directory: Database
2025-05-20 07:16:55 [INFO] Validated directory: Images
2025-05-20 07:16:56 [INFO] Validated directory: Logs
2025-05-20 07:16:56 [INFO] Validated directory: Temporary files
2025-05-20 07:16:56 [INFO] Validated directory: Candidate images
2025-05-20 07:16:56 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:16:56 [INFO] All required files and directories validated successfully
2025-05-20 07:16:56 [INFO] Initializing application components
2025-05-20 07:16:56 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:16:56 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:16:56 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:16:56 [INFO] Room cache loaded with 3 entries
2025-05-20 07:16:56 [INFO] Loading seat assignments for date: 20250520
2025-05-20 07:16:56 [INFO] No seat assignments found for today (20250520)
2025-05-20 07:16:56 [INFO] Database manager initialized successfully
2025-05-20 07:16:56 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:16:56 [INFO] Webcam manager initialized
2025-05-20 07:16:56 [INFO] Webcam: Starting webcam...
2025-05-20 07:16:56 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:16:56 [INFO] Webcam started successfully
2025-05-20 07:16:56 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:16:56 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:16:56 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:16:56 [INFO] Post-exam mode is enabled
2025-05-20 07:16:56 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:16:56 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:16:56 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:16:56 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:16:56 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:16:56 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:16:56 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:16:56 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:16:56 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:16:56 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:16:56 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:16:56 [INFO] Added webcam feed control with default photo
2025-05-20 07:16:56 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:16:59 [INFO] No seat assignment found for 9351
2025-05-20 07:17:01 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 07:17:01 [DEBUG] Room Cache: 3 entries
2025-05-20 07:17:01 [DEBUG] Hardware Cache: 10 entries
2025-05-20 07:17:01 [DEBUG] Seat Assignment Cache: 0 entries
2025-05-20 07:17:01 [INFO] Seat assigned successfully for 9351: F1-R1-S5 (Special Needs allocation)
2025-05-20 07:18:14 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:18:14 [INFO] Error handler initialized
2025-05-20 07:18:14 [INFO] Read database path from config: db
2025-05-20 07:18:14 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:18:14 [INFO] Error handler initialized
2025-05-20 07:18:14 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 07:18:14 [INFO] PathManager initialized successfully
2025-05-20 07:18:14 [INFO] PathManager initialized successfully
2025-05-20 07:18:14 [INFO] Validating required files and directories
2025-05-20 07:18:14 [INFO] Validated directory: Database
2025-05-20 07:18:15 [INFO] Validated directory: Images
2025-05-20 07:18:15 [INFO] Validated directory: Logs
2025-05-20 07:18:15 [INFO] Validated directory: Temporary files
2025-05-20 07:18:15 [INFO] Validated directory: Candidate images
2025-05-20 07:18:15 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:18:15 [INFO] All required files and directories validated successfully
2025-05-20 07:18:15 [INFO] Initializing application components
2025-05-20 07:18:15 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:18:15 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:18:15 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:18:15 [INFO] Room cache loaded with 3 entries
2025-05-20 07:18:15 [INFO] Loading seat assignments for date: 20250520
2025-05-20 07:18:15 [INFO] Loaded 1 seat assignments for today
2025-05-20 07:18:15 [INFO] Database manager initialized successfully
2025-05-20 07:18:15 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:18:15 [INFO] Webcam manager initialized
2025-05-20 07:18:15 [INFO] Webcam: Starting webcam...
2025-05-20 07:18:15 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:18:15 [INFO] Webcam started successfully
2025-05-20 07:18:15 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:18:15 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:18:15 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:18:15 [INFO] Post-exam mode is enabled
2025-05-20 07:18:15 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:18:15 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:18:15 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:18:15 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:18:15 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:18:15 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:18:15 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:18:15 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:18:15 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:18:15 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:18:15 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:18:15 [INFO] Added webcam feed control with default photo
2025-05-20 07:18:15 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:18:20 [INFO] Found seat assignment for 9351: F1-R1-S5
2025-05-20 07:18:32 [INFO] No seat assignment found for 9351
2025-05-20 07:18:34 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 07:18:34 [DEBUG] Room Cache: 3 entries
2025-05-20 07:18:34 [DEBUG] Hardware Cache: 10 entries
2025-05-20 07:18:34 [DEBUG] Seat Assignment Cache: 1 entries
2025-05-20 07:18:35 [INFO] Seat assigned successfully for 9351: F1-R1-S1 (Special Needs allocation)
2025-05-20 07:21:46 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:21:47 [INFO] Error handler initialized
2025-05-20 07:21:47 [INFO] Read database path from config: db
2025-05-20 07:21:47 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:21:47 [INFO] Error handler initialized
2025-05-20 07:21:47 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 07:21:47 [INFO] PathManager initialized successfully
2025-05-20 07:21:47 [INFO] PathManager initialized successfully
2025-05-20 07:21:47 [INFO] Validating required files and directories
2025-05-20 07:21:47 [INFO] Validated directory: Database
2025-05-20 07:21:47 [INFO] Validated directory: Images
2025-05-20 07:21:47 [INFO] Validated directory: Logs
2025-05-20 07:21:47 [INFO] Validated directory: Temporary files
2025-05-20 07:21:47 [INFO] Validated directory: Candidate images
2025-05-20 07:21:47 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:21:47 [INFO] All required files and directories validated successfully
2025-05-20 07:21:47 [INFO] Initializing application components
2025-05-20 07:21:47 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:21:47 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:21:47 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:21:47 [INFO] Room cache loaded with 3 entries
2025-05-20 07:21:47 [INFO] Loading seat assignments for date: 20250520
2025-05-20 07:21:47 [INFO] Loaded 1 seat assignments for today
2025-05-20 07:21:47 [INFO] Database manager initialized successfully
2025-05-20 07:21:47 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:21:47 [INFO] Webcam manager initialized
2025-05-20 07:21:47 [INFO] Webcam: Starting webcam...
2025-05-20 07:21:47 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:21:47 [INFO] Webcam started successfully
2025-05-20 07:21:47 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:21:47 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:21:47 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:21:47 [INFO] Post-exam mode is enabled
2025-05-20 07:21:47 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:21:47 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:21:47 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:21:47 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:21:47 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:21:47 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:21:47 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:21:47 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:21:47 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:21:47 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:21:47 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:21:47 [INFO] Added webcam feed control with default photo
2025-05-20 07:21:48 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:21:58 [INFO] No seat assignment found for 9351
2025-05-20 07:21:59 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 07:21:59 [DEBUG] Room Cache: 3 entries
2025-05-20 07:21:59 [DEBUG] Hardware Cache: 10 entries
2025-05-20 07:21:59 [DEBUG] Seat Assignment Cache: 1 entries
2025-05-20 07:21:59 [INFO] Seat assigned successfully for 9351: F1-R1-S8 (Special Needs allocation)
2025-05-20 07:22:33 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:22:33 [INFO] Error handler initialized
2025-05-20 07:22:33 [INFO] Read database path from config: db
2025-05-20 07:22:34 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:22:34 [INFO] Error handler initialized
2025-05-20 07:22:34 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 07:22:34 [INFO] PathManager initialized successfully
2025-05-20 07:22:34 [INFO] PathManager initialized successfully
2025-05-20 07:22:34 [INFO] Validating required files and directories
2025-05-20 07:22:34 [INFO] Validated directory: Database
2025-05-20 07:22:34 [INFO] Validated directory: Images
2025-05-20 07:22:34 [INFO] Validated directory: Logs
2025-05-20 07:22:34 [INFO] Validated directory: Temporary files
2025-05-20 07:22:34 [INFO] Validated directory: Candidate images
2025-05-20 07:22:34 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:22:34 [INFO] All required files and directories validated successfully
2025-05-20 07:22:34 [INFO] Initializing application components
2025-05-20 07:22:34 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:22:34 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:22:34 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:22:34 [INFO] Room cache loaded with 3 entries
2025-05-20 07:22:34 [INFO] Loading seat assignments for date: 20250520
2025-05-20 07:22:34 [INFO] Loaded 1 seat assignments for today
2025-05-20 07:22:34 [INFO] Database manager initialized successfully
2025-05-20 07:22:34 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:22:34 [INFO] Webcam manager initialized
2025-05-20 07:22:34 [INFO] Webcam: Starting webcam...
2025-05-20 07:22:34 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:22:34 [INFO] Webcam started successfully
2025-05-20 07:22:34 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:22:34 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:22:34 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:22:34 [INFO] Post-exam mode is enabled
2025-05-20 07:22:34 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:22:34 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:22:34 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:22:34 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:22:34 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:22:34 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:22:34 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:22:34 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:22:34 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:22:34 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:22:34 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:22:34 [INFO] Added webcam feed control with default photo
2025-05-20 07:22:34 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:22:46 [INFO] No seat assignment found for 9351
2025-05-20 07:22:47 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 07:22:47 [DEBUG] Room Cache: 3 entries
2025-05-20 07:22:47 [DEBUG] Hardware Cache: 10 entries
2025-05-20 07:22:47 [DEBUG] Seat Assignment Cache: 1 entries
2025-05-20 07:22:47 [INFO] Seat assigned successfully for 9351: F1-R1-S1 (Special Needs allocation)
2025-05-20 07:26:38 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:26:38 [INFO] Error handler initialized
2025-05-20 07:26:38 [INFO] Read database path from config: db
2025-05-20 07:26:38 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:26:38 [INFO] Error handler initialized
2025-05-20 07:26:38 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-20 07:26:38 [INFO] PathManager initialized successfully
2025-05-20 07:26:38 [INFO] PathManager initialized successfully
2025-05-20 07:26:38 [INFO] Validating required files and directories
2025-05-20 07:26:38 [INFO] Validated directory: Database
2025-05-20 07:26:38 [INFO] Validated directory: Images
2025-05-20 07:26:38 [INFO] Validated directory: Logs
2025-05-20 07:26:38 [INFO] Validated directory: Temporary files
2025-05-20 07:26:38 [INFO] Validated directory: Candidate images
2025-05-20 07:26:38 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:26:38 [INFO] All required files and directories validated successfully
2025-05-20 07:26:38 [INFO] Initializing application components
2025-05-20 07:26:38 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:26:38 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:26:38 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:26:38 [INFO] Room cache loaded with 3 entries
2025-05-20 07:26:39 [INFO] Loading seat assignments for date: 20250520
2025-05-20 07:26:39 [INFO] No seat assignments found for today (20250520)
2025-05-20 07:26:39 [INFO] Database manager initialized successfully
2025-05-20 07:26:39 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:26:39 [INFO] Webcam manager initialized
2025-05-20 07:26:39 [INFO] Webcam: Starting webcam...
2025-05-20 07:26:39 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:26:39 [INFO] Webcam started successfully
2025-05-20 07:26:39 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:26:39 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:26:39 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:26:39 [INFO] Post-exam mode is enabled
2025-05-20 07:26:39 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:26:39 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:26:39 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:26:39 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:26:39 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:26:39 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:26:39 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:26:39 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:26:39 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:26:39 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:26:39 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:26:39 [INFO] Added webcam feed control with default photo
2025-05-20 07:26:40 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:26:42 [INFO] No seat assignment found for 9351
2025-05-20 07:26:44 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 07:26:44 [DEBUG] Room Cache: 3 entries
2025-05-20 07:26:44 [DEBUG] Hardware Cache: 10 entries
2025-05-20 07:26:44 [DEBUG] Seat Assignment Cache: 0 entries
2025-05-20 07:26:44 [INFO] Seat assigned successfully for 9351: F1-R1-S7 (Special Needs allocation)
2025-05-20 07:34:57 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:34:57 [INFO] Error handler initialized
2025-05-20 07:34:57 [INFO] Read database path from config: db
2025-05-20 07:34:57 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:34:57 [INFO] Error handler initialized
2025-05-20 07:34:57 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 07:34:57 [INFO] PathManager initialized successfully
2025-05-20 07:34:58 [INFO] PathManager initialized successfully
2025-05-20 07:34:58 [INFO] Validating required files and directories
2025-05-20 07:34:58 [INFO] Validated directory: Database
2025-05-20 07:34:58 [INFO] Validated directory: Images
2025-05-20 07:34:58 [INFO] Validated directory: Logs
2025-05-20 07:34:58 [INFO] Validated directory: Temporary files
2025-05-20 07:34:58 [INFO] Validated directory: Candidate images
2025-05-20 07:34:58 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:34:58 [INFO] All required files and directories validated successfully
2025-05-20 07:34:58 [INFO] Initializing application components
2025-05-20 07:34:58 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:34:58 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:34:58 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:34:58 [INFO] Room cache loaded with 3 entries
2025-05-20 07:34:58 [INFO] Loading seat assignments from seat ID sections
2025-05-20 07:34:58 [INFO] Loaded 1 seat assignments
2025-05-20 07:34:58 [INFO] Database manager initialized successfully
2025-05-20 07:34:58 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:34:58 [INFO] Webcam manager initialized
2025-05-20 07:34:58 [INFO] Webcam: Starting webcam...
2025-05-20 07:34:58 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:34:58 [INFO] Webcam started successfully
2025-05-20 07:34:58 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:34:58 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:34:58 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:34:58 [INFO] Post-exam mode is enabled
2025-05-20 07:34:58 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:34:58 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:34:58 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:34:58 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:34:58 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:34:58 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:34:58 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:34:59 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:34:59 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:34:59 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:34:59 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:34:59 [INFO] Added webcam feed control with default photo
2025-05-20 07:34:59 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:35:13 [INFO] Found seat assignment in cache for 9351: F1-R1-S7
2025-05-20 07:35:37 [INFO] Found seat assignment in cache for 9351: F1-R1-S7
2025-05-20 07:36:04 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:36:04 [INFO] Error handler initialized
2025-05-20 07:36:04 [INFO] Read database path from config: db
2025-05-20 07:36:04 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:36:04 [INFO] Error handler initialized
2025-05-20 07:36:04 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 07:36:04 [INFO] PathManager initialized successfully
2025-05-20 07:36:04 [INFO] PathManager initialized successfully
2025-05-20 07:36:04 [INFO] Validating required files and directories
2025-05-20 07:36:04 [INFO] Validated directory: Database
2025-05-20 07:36:04 [INFO] Validated directory: Images
2025-05-20 07:36:05 [INFO] Validated directory: Logs
2025-05-20 07:36:05 [INFO] Validated directory: Temporary files
2025-05-20 07:36:05 [INFO] Validated directory: Candidate images
2025-05-20 07:36:05 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:36:05 [INFO] All required files and directories validated successfully
2025-05-20 07:36:05 [INFO] Initializing application components
2025-05-20 07:36:05 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:36:05 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:36:05 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:36:05 [INFO] Room cache loaded with 3 entries
2025-05-20 07:36:05 [INFO] Loading seat assignments from seat ID sections
2025-05-20 07:36:05 [INFO] Loaded 0 seat assignments
2025-05-20 07:36:05 [INFO] Database manager initialized successfully
2025-05-20 07:36:05 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:36:05 [INFO] Webcam manager initialized
2025-05-20 07:36:05 [INFO] Webcam: Starting webcam...
2025-05-20 07:36:05 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:36:05 [INFO] Webcam started successfully
2025-05-20 07:36:05 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:36:05 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:36:05 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:36:05 [INFO] Post-exam mode is enabled
2025-05-20 07:36:05 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:36:05 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:36:05 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:36:05 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:36:05 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:36:05 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:36:05 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:36:05 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:36:05 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:36:05 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:36:05 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:36:05 [INFO] Added webcam feed control with default photo
2025-05-20 07:36:06 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:36:09 [INFO] No seat assignment found for 9351
2025-05-20 07:36:19 [INFO] Webcam: Webcam stopped
2025-05-20 07:36:19 [INFO] Webcam: Capturing image via FFmpeg from camera: HD Pro Webcam C920
2025-05-20 07:36:21 [INFO] Webcam: Image captured successfully: C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250520_073619.jpg
2025-05-20 07:36:23 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9351_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250520_073619.jpg
2025-05-20 07:36:23 [ERROR] Registered photo not found: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9351_photo.jpg
2025-05-20 07:36:26 [INFO] Verifying photo: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\candidates\9351_registered_photo.jpg against C:\Users\<USER>\AppData\Local\Temp\wincbt_biometric\captured_20250520_073619.jpg
2025-05-20 07:36:26 [INFO] Photo verification FAILED with confidence 70%
2025-05-20 07:36:37 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 07:36:37 [INFO] No seat assignment found for 9351
2025-05-20 07:36:37 [DEBUG] Room Cache: 3 entries
2025-05-20 07:36:37 [DEBUG] Hardware Cache: 10 entries
2025-05-20 07:36:37 [DEBUG] Seat Assignment Cache: 0 entries
2025-05-20 07:36:38 [INFO] Seat assigned successfully for 9351: F1-R1-S5 (Special Needs allocation)
2025-05-20 07:36:52 [INFO] Found seat assignment in cache for 9351: F1-R1-S5
2025-05-20 07:37:28 [INFO] Found seat assignment in cache for 9351: F1-R1-S5
2025-05-20 07:37:38 [INFO] Seat assignments file not found when checking for roll number: 9351
2025-05-20 07:37:56 [INFO] Found seat assignment in cache for 9351: F1-R1-S5
2025-05-20 07:38:14 [INFO] No seat assignment found for 9353
2025-05-20 07:38:20 [INFO] Found seat assignment in cache for 9351: F1-R1-S5
2025-05-20 07:38:56 [INFO] Found seat assignment in cache for 9351: F1-R1-S5
2025-05-20 07:39:13 [INFO] Found seat assignment in cache for 9351: F1-R1-S5
2025-05-20 07:39:20 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:39:20 [INFO] Error handler initialized
2025-05-20 07:39:20 [INFO] Read database path from config: db
2025-05-20 07:39:20 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:39:20 [INFO] Error handler initialized
2025-05-20 07:39:20 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 07:39:20 [INFO] PathManager initialized successfully
2025-05-20 07:39:20 [INFO] PathManager initialized successfully
2025-05-20 07:39:20 [INFO] Validating required files and directories
2025-05-20 07:39:20 [INFO] Validated directory: Database
2025-05-20 07:39:20 [INFO] Validated directory: Images
2025-05-20 07:39:20 [INFO] Validated directory: Logs
2025-05-20 07:39:20 [INFO] Validated directory: Temporary files
2025-05-20 07:39:20 [INFO] Validated directory: Candidate images
2025-05-20 07:39:20 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:39:20 [INFO] All required files and directories validated successfully
2025-05-20 07:39:20 [INFO] Initializing application components
2025-05-20 07:39:20 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:39:20 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:39:20 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:39:20 [INFO] Room cache loaded with 3 entries
2025-05-20 07:39:20 [INFO] Loading seat assignments from seat ID sections
2025-05-20 07:39:20 [INFO] Loaded 0 seat assignments
2025-05-20 07:39:20 [INFO] Database manager initialized successfully
2025-05-20 07:39:20 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:39:20 [INFO] Webcam manager initialized
2025-05-20 07:39:20 [INFO] Webcam: Starting webcam...
2025-05-20 07:39:21 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:39:21 [INFO] Webcam started successfully
2025-05-20 07:39:21 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:39:21 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:39:21 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:39:21 [INFO] Post-exam mode is enabled
2025-05-20 07:39:21 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:39:21 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:39:21 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:39:21 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:39:21 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:39:21 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:39:21 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:39:21 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:39:21 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:39:21 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:39:21 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:39:21 [INFO] Added webcam feed control with default photo
2025-05-20 07:39:21 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:39:25 [INFO] No seat assignment found for 9351
2025-05-20 07:39:38 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 07:39:54 [INFO] Error handler initialized
2025-05-20 07:39:54 [INFO] Read database path from config: db
2025-05-20 07:39:54 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:39:54 [INFO] Error handler initialized
2025-05-20 07:39:54 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 07:39:54 [INFO] PathManager initialized successfully
2025-05-20 07:39:54 [INFO] PathManager initialized successfully
2025-05-20 07:39:54 [INFO] Validating required files and directories
2025-05-20 07:39:54 [INFO] Validated directory: Database
2025-05-20 07:39:54 [INFO] Validated directory: Images
2025-05-20 07:39:54 [INFO] Validated directory: Logs
2025-05-20 07:39:54 [INFO] Validated directory: Temporary files
2025-05-20 07:39:54 [INFO] Validated directory: Candidate images
2025-05-20 07:39:54 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:39:54 [INFO] All required files and directories validated successfully
2025-05-20 07:39:54 [INFO] Initializing application components
2025-05-20 07:39:54 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:39:54 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:39:54 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:39:54 [INFO] Room cache loaded with 3 entries
2025-05-20 07:39:54 [INFO] Loading seat assignments from seat ID sections
2025-05-20 07:39:54 [INFO] Loaded 0 seat assignments
2025-05-20 07:39:54 [INFO] Database manager initialized successfully
2025-05-20 07:39:54 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:39:54 [INFO] Webcam manager initialized
2025-05-20 07:39:54 [INFO] Webcam: Starting webcam...
2025-05-20 07:39:55 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:39:55 [INFO] Webcam started successfully
2025-05-20 07:39:55 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:39:55 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:39:55 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:39:55 [INFO] Post-exam mode is enabled
2025-05-20 07:39:55 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:39:55 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:39:55 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:39:55 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:39:55 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:39:55 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:39:55 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:39:55 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:39:55 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:39:55 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:39:55 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:39:55 [INFO] Added webcam feed control with default photo
2025-05-20 07:39:55 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:39:57 [INFO] No seat assignment found for 9351
2025-05-20 07:40:30 [INFO] Found seat assignment for 9351: F1-R1-S5
2025-05-20 07:40:46 [INFO] Found seat assignment in cache for 9351: F1-R1-S5
2025-05-20 07:40:50 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:40:50 [INFO] Error handler initialized
2025-05-20 07:40:50 [INFO] Read database path from config: db
2025-05-20 07:40:50 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:40:50 [INFO] Error handler initialized
2025-05-20 07:40:50 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 07:40:50 [INFO] PathManager initialized successfully
2025-05-20 07:40:50 [INFO] PathManager initialized successfully
2025-05-20 07:40:50 [INFO] Validating required files and directories
2025-05-20 07:40:50 [INFO] Validated directory: Database
2025-05-20 07:40:50 [INFO] Validated directory: Images
2025-05-20 07:40:50 [INFO] Validated directory: Logs
2025-05-20 07:40:50 [INFO] Validated directory: Temporary files
2025-05-20 07:40:50 [INFO] Validated directory: Candidate images
2025-05-20 07:40:50 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:40:50 [INFO] All required files and directories validated successfully
2025-05-20 07:40:50 [INFO] Initializing application components
2025-05-20 07:40:50 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:40:50 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:40:50 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:40:50 [INFO] Room cache loaded with 3 entries
2025-05-20 07:40:50 [INFO] Loading seat assignments from seat ID sections
2025-05-20 07:40:50 [INFO] Loaded 0 seat assignments
2025-05-20 07:40:50 [INFO] Database manager initialized successfully
2025-05-20 07:40:50 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:40:50 [INFO] Webcam manager initialized
2025-05-20 07:40:50 [INFO] Webcam: Starting webcam...
2025-05-20 07:40:51 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:40:51 [INFO] Webcam started successfully
2025-05-20 07:40:51 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:40:51 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:40:51 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:40:51 [INFO] Post-exam mode is enabled
2025-05-20 07:40:51 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:40:51 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:40:51 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:40:51 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:40:51 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:40:51 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:40:51 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:40:51 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:40:51 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:40:51 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:40:51 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:40:51 [INFO] Added webcam feed control with default photo
2025-05-20 07:40:51 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:40:54 [INFO] No seat assignment found for 9351
2025-05-20 07:41:29 [INFO] Found seat assignment for 9351: F1-R1-S5
2025-05-20 07:42:27 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:42:28 [INFO] Error handler initialized
2025-05-20 07:42:28 [INFO] Read database path from config: db
2025-05-20 07:42:28 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:42:28 [INFO] Error handler initialized
2025-05-20 07:42:28 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 07:42:28 [INFO] PathManager initialized successfully
2025-05-20 07:42:28 [INFO] PathManager initialized successfully
2025-05-20 07:42:28 [INFO] Validating required files and directories
2025-05-20 07:42:28 [INFO] Validated directory: Database
2025-05-20 07:42:28 [INFO] Validated directory: Images
2025-05-20 07:42:28 [INFO] Validated directory: Logs
2025-05-20 07:42:28 [INFO] Validated directory: Temporary files
2025-05-20 07:42:28 [INFO] Validated directory: Candidate images
2025-05-20 07:42:28 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:42:28 [INFO] All required files and directories validated successfully
2025-05-20 07:42:28 [INFO] Initializing application components
2025-05-20 07:42:28 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:42:28 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:42:28 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:42:28 [INFO] Room cache loaded with 3 entries
2025-05-20 07:42:28 [INFO] Loading seat assignments from seat ID sections
2025-05-20 07:42:28 [INFO] Loaded 1 seat assignments
2025-05-20 07:42:28 [INFO] Database manager initialized successfully
2025-05-20 07:42:28 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:42:28 [INFO] Webcam manager initialized
2025-05-20 07:42:28 [INFO] Webcam: Starting webcam...
2025-05-20 07:42:28 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:42:28 [INFO] Webcam started successfully
2025-05-20 07:42:29 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:42:29 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:42:29 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:42:29 [INFO] Post-exam mode is enabled
2025-05-20 07:42:29 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:42:29 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:42:29 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:42:29 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:42:29 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:42:29 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:42:29 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:42:29 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:42:29 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:42:29 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:42:30 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:42:31 [INFO] Added webcam feed control with default photo
2025-05-20 07:42:31 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:43:06 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:43:07 [INFO] Error handler initialized
2025-05-20 07:43:07 [INFO] Read database path from config: db
2025-05-20 07:43:07 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:43:07 [INFO] Error handler initialized
2025-05-20 07:43:07 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 07:43:07 [INFO] PathManager initialized successfully
2025-05-20 07:43:07 [INFO] PathManager initialized successfully
2025-05-20 07:43:07 [INFO] Validating required files and directories
2025-05-20 07:43:07 [INFO] Validated directory: Database
2025-05-20 07:43:07 [INFO] Validated directory: Images
2025-05-20 07:43:07 [INFO] Validated directory: Logs
2025-05-20 07:43:07 [INFO] Validated directory: Temporary files
2025-05-20 07:43:07 [INFO] Validated directory: Candidate images
2025-05-20 07:43:07 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:43:07 [INFO] All required files and directories validated successfully
2025-05-20 07:43:07 [INFO] Initializing application components
2025-05-20 07:43:07 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:43:07 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:43:07 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:43:07 [INFO] Room cache loaded with 3 entries
2025-05-20 07:43:07 [INFO] Loading seat assignments from seat ID sections
2025-05-20 07:43:07 [INFO] Loaded 1 seat assignments
2025-05-20 07:43:07 [INFO] Database manager initialized successfully
2025-05-20 07:43:07 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:43:07 [INFO] Webcam manager initialized
2025-05-20 07:43:07 [INFO] Webcam: Starting webcam...
2025-05-20 07:43:08 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:43:08 [INFO] Webcam started successfully
2025-05-20 07:43:08 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:43:08 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:43:08 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:43:08 [INFO] Post-exam mode is enabled
2025-05-20 07:43:08 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:43:08 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:43:08 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:43:08 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:43:08 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:43:08 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:43:08 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:43:08 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:43:08 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:43:08 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:43:08 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:43:08 [INFO] Added webcam feed control with default photo
2025-05-20 07:43:08 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:43:22 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:43:23 [INFO] Error handler initialized
2025-05-20 07:43:23 [INFO] Read database path from config: db
2025-05-20 07:43:23 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:43:23 [INFO] Error handler initialized
2025-05-20 07:43:23 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 07:43:23 [INFO] PathManager initialized successfully
2025-05-20 07:43:23 [INFO] PathManager initialized successfully
2025-05-20 07:43:23 [INFO] Validating required files and directories
2025-05-20 07:43:23 [INFO] Validated directory: Database
2025-05-20 07:43:23 [INFO] Validated directory: Images
2025-05-20 07:43:23 [INFO] Validated directory: Logs
2025-05-20 07:43:23 [INFO] Validated directory: Temporary files
2025-05-20 07:43:23 [INFO] Validated directory: Candidate images
2025-05-20 07:43:23 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:43:23 [INFO] All required files and directories validated successfully
2025-05-20 07:43:23 [INFO] Initializing application components
2025-05-20 07:43:23 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:43:23 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:43:23 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:43:23 [INFO] Room cache loaded with 3 entries
2025-05-20 07:43:23 [INFO] Loading seat assignments from seat ID sections
2025-05-20 07:43:23 [INFO] Loaded 1 seat assignments
2025-05-20 07:43:23 [INFO] Database manager initialized successfully
2025-05-20 07:43:23 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:43:23 [INFO] Webcam manager initialized
2025-05-20 07:43:23 [INFO] Webcam: Starting webcam...
2025-05-20 07:43:23 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:43:23 [INFO] Webcam started successfully
2025-05-20 07:43:23 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:43:23 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:43:23 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:43:23 [INFO] Post-exam mode is enabled
2025-05-20 07:43:24 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:43:24 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:43:24 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:43:24 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:43:24 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:43:24 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:43:24 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:43:24 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:43:24 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:43:24 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:43:24 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:43:24 [INFO] Added webcam feed control with default photo
2025-05-20 07:43:24 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:43:38 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:43:38 [INFO] Error handler initialized
2025-05-20 07:43:38 [INFO] Read database path from config: db
2025-05-20 07:43:38 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:43:38 [INFO] Error handler initialized
2025-05-20 07:43:38 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 07:43:38 [INFO] PathManager initialized successfully
2025-05-20 07:43:38 [INFO] PathManager initialized successfully
2025-05-20 07:43:38 [INFO] Validating required files and directories
2025-05-20 07:43:38 [INFO] Validated directory: Database
2025-05-20 07:43:38 [INFO] Validated directory: Images
2025-05-20 07:43:38 [INFO] Validated directory: Logs
2025-05-20 07:43:38 [INFO] Validated directory: Temporary files
2025-05-20 07:43:38 [INFO] Validated directory: Candidate images
2025-05-20 07:43:38 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:43:38 [INFO] All required files and directories validated successfully
2025-05-20 07:43:38 [INFO] Initializing application components
2025-05-20 07:43:38 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:43:38 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:43:38 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:43:38 [INFO] Room cache loaded with 3 entries
2025-05-20 07:43:38 [INFO] Loading seat assignments from seat ID sections
2025-05-20 07:43:38 [INFO] Loaded 1 seat assignments
2025-05-20 07:43:38 [INFO] Database manager initialized successfully
2025-05-20 07:43:38 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:43:38 [INFO] Webcam manager initialized
2025-05-20 07:43:38 [INFO] Webcam: Starting webcam...
2025-05-20 07:43:39 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:43:39 [INFO] Webcam started successfully
2025-05-20 07:43:39 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:43:39 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:43:39 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:43:39 [INFO] Post-exam mode is enabled
2025-05-20 07:43:39 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:43:39 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:43:39 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:43:39 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:43:39 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:43:39 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:43:39 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:43:39 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:43:39 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:43:39 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:43:40 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:43:40 [INFO] Added webcam feed control with default photo
2025-05-20 07:43:40 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:49:43 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:49:43 [INFO] Error handler initialized
2025-05-20 07:49:43 [INFO] Read database path from config: db
2025-05-20 07:49:43 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:49:43 [INFO] Error handler initialized
2025-05-20 07:49:43 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 07:49:43 [INFO] Migrated settings from config.ini to WinCBT-Biometric.ini
2025-05-20 07:49:43 [INFO] PathManager initialized successfully
2025-05-20 07:49:43 [INFO] PathManager initialized successfully
2025-05-20 07:49:43 [INFO] Validating required files and directories
2025-05-20 07:49:43 [INFO] Validated directory: Database
2025-05-20 07:49:43 [INFO] Validated directory: Images
2025-05-20 07:49:43 [INFO] Validated directory: Logs
2025-05-20 07:49:43 [INFO] Validated directory: Temporary files
2025-05-20 07:49:43 [INFO] Validated directory: Candidate images
2025-05-20 07:49:43 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:49:43 [INFO] All required files and directories validated successfully
2025-05-20 07:49:43 [INFO] Initializing application components
2025-05-20 07:49:43 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:49:43 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:49:43 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:49:43 [INFO] Room cache loaded with 3 entries
2025-05-20 07:49:43 [INFO] Loading seat assignments from seat ID sections
2025-05-20 07:49:43 [INFO] Loaded 1 seat assignments
2025-05-20 07:49:43 [INFO] Database manager initialized successfully
2025-05-20 07:49:43 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:49:43 [INFO] Webcam manager initialized
2025-05-20 07:49:43 [INFO] Webcam: Starting webcam...
2025-05-20 07:49:44 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:49:44 [INFO] Webcam started successfully
2025-05-20 07:49:44 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:49:44 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:49:44 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:49:44 [INFO] Post-exam mode is enabled
2025-05-20 07:49:44 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:49:44 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:49:44 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:49:44 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:49:44 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:49:44 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:49:44 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:49:44 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:49:44 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:49:44 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:49:44 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:49:44 [INFO] Added webcam feed control with default photo
2025-05-20 07:49:44 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:50:25 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:50:25 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:50:25 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:50:25 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:50:25 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:50:25 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:50:48 [INFO] Application exiting: Single (Code: 0)
2025-05-20 07:50:48 [INFO] Error handler initialized
2025-05-20 07:50:48 [INFO] Read database path from config: db
2025-05-20 07:50:48 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 07:50:48 [INFO] Error handler initialized
2025-05-20 07:50:48 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 07:50:48 [INFO] PathManager initialized successfully
2025-05-20 07:50:48 [INFO] PathManager initialized successfully
2025-05-20 07:50:49 [INFO] Validating required files and directories
2025-05-20 07:50:49 [INFO] Validated directory: Database
2025-05-20 07:50:49 [INFO] Validated directory: Images
2025-05-20 07:50:49 [INFO] Validated directory: Logs
2025-05-20 07:50:49 [INFO] Validated directory: Temporary files
2025-05-20 07:50:49 [INFO] Validated directory: Candidate images
2025-05-20 07:50:49 [INFO] Validated directory: Fingerprint templates
2025-05-20 07:50:49 [INFO] All required files and directories validated successfully
2025-05-20 07:50:49 [INFO] Initializing application components
2025-05-20 07:50:49 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 07:50:49 [INFO] Hardware cache loaded with 10 entries
2025-05-20 07:50:49 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 07:50:49 [INFO] Room cache loaded with 3 entries
2025-05-20 07:50:49 [INFO] Loading seat assignments from seat ID sections
2025-05-20 07:50:49 [INFO] Loaded 1 seat assignments
2025-05-20 07:50:49 [INFO] Database manager initialized successfully
2025-05-20 07:50:49 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 07:50:49 [INFO] Webcam manager initialized
2025-05-20 07:50:49 [INFO] Webcam: Starting webcam...
2025-05-20 07:50:49 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 07:50:49 [INFO] Webcam started successfully
2025-05-20 07:50:49 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:50:49 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:50:49 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:50:50 [INFO] Post-exam mode is enabled
2025-05-20 07:50:50 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 07:50:50 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 07:50:50 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 07:50:50 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 07:50:50 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:50:50 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:50:50 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 07:50:50 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 07:50:50 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 07:50:50 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 07:50:50 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 07:50:50 [INFO] Added webcam feed control with default photo
2025-05-20 07:50:50 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 07:50:59 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 07:50:59 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 07:50:59 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 07:50:59 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 07:50:59 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 07:50:59 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:00:57 [INFO] Application exiting: Single (Code: 0)
2025-05-20 08:00:57 [INFO] Error handler initialized
2025-05-20 08:00:57 [INFO] Read database path from config: db
2025-05-20 08:00:57 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:00:57 [INFO] Error handler initialized
2025-05-20 08:00:57 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:00:57 [INFO] PathManager initialized successfully
2025-05-20 08:00:57 [INFO] PathManager initialized successfully
2025-05-20 08:00:57 [INFO] Validating required files and directories
2025-05-20 08:00:57 [INFO] Validated directory: Database
2025-05-20 08:00:57 [INFO] Validated directory: Images
2025-05-20 08:00:57 [INFO] Validated directory: Logs
2025-05-20 08:00:57 [INFO] Validated directory: Temporary files
2025-05-20 08:00:57 [INFO] Validated directory: Candidate images
2025-05-20 08:00:57 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:00:57 [INFO] All required files and directories validated successfully
2025-05-20 08:00:57 [INFO] Initializing application components
2025-05-20 08:00:57 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:00:57 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:00:57 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:00:57 [INFO] Room cache loaded with 3 entries
2025-05-20 08:00:57 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:00:57 [INFO] Loaded 1 seat assignments
2025-05-20 08:00:57 [INFO] Database manager initialized successfully
2025-05-20 08:00:57 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:00:58 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:00:58 [INFO] Webcam started successfully
2025-05-20 08:00:58 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:00:58 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:00:58 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 08:00:58 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:00:58 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:00:58 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:00:58 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:00:58 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:00:58 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:00:58 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:00:58 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:00:58 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:00:58 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:00:58 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:00:58 [INFO] Added webcam feed control with default photo
2025-05-20 08:00:58 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:01:06 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:02:04 [INFO] Error handler initialized
2025-05-20 08:02:04 [INFO] Read database path from config: db
2025-05-20 08:02:04 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:02:04 [INFO] Error handler initialized
2025-05-20 08:02:04 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:02:04 [INFO] PathManager initialized successfully
2025-05-20 08:02:04 [INFO] PathManager initialized successfully
2025-05-20 08:02:04 [INFO] Validating required files and directories
2025-05-20 08:02:04 [INFO] Validated directory: Database
2025-05-20 08:02:04 [INFO] Validated directory: Images
2025-05-20 08:02:04 [INFO] Validated directory: Logs
2025-05-20 08:02:04 [INFO] Validated directory: Temporary files
2025-05-20 08:02:04 [INFO] Validated directory: Candidate images
2025-05-20 08:02:04 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:02:04 [INFO] All required files and directories validated successfully
2025-05-20 08:02:04 [INFO] Initializing application components
2025-05-20 08:02:04 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:02:04 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:02:04 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:02:04 [INFO] Room cache loaded with 3 entries
2025-05-20 08:02:04 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:02:04 [INFO] Loaded 1 seat assignments
2025-05-20 08:02:04 [INFO] Database manager initialized successfully
2025-05-20 08:02:04 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:02:04 [INFO] Webcam manager initialized
2025-05-20 08:02:04 [INFO] Webcam: Starting webcam...
2025-05-20 08:02:05 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:02:05 [INFO] Webcam started successfully
2025-05-20 08:02:05 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:02:05 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:02:05 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 08:02:05 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:02:05 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:02:05 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:02:05 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:02:05 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:02:05 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:02:05 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:02:05 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:02:05 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:02:05 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:02:05 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:02:05 [INFO] Added webcam feed control with default photo
2025-05-20 08:02:05 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:02:24 [INFO] About dialog closed
2025-05-20 08:02:55 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:03:11 [INFO] Error handler initialized
2025-05-20 08:03:11 [INFO] Read database path from config: db
2025-05-20 08:03:11 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:03:11 [INFO] Error handler initialized
2025-05-20 08:03:11 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:03:11 [INFO] PathManager initialized successfully
2025-05-20 08:03:11 [INFO] PathManager initialized successfully
2025-05-20 08:03:11 [INFO] Validating required files and directories
2025-05-20 08:03:11 [INFO] Validated directory: Database
2025-05-20 08:03:11 [INFO] Validated directory: Images
2025-05-20 08:03:11 [INFO] Validated directory: Logs
2025-05-20 08:03:11 [INFO] Validated directory: Temporary files
2025-05-20 08:03:11 [INFO] Validated directory: Candidate images
2025-05-20 08:03:11 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:03:11 [INFO] All required files and directories validated successfully
2025-05-20 08:03:11 [INFO] Initializing application components
2025-05-20 08:03:11 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:03:11 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:03:11 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:03:11 [INFO] Room cache loaded with 3 entries
2025-05-20 08:03:11 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:03:11 [INFO] Loaded 1 seat assignments
2025-05-20 08:03:11 [INFO] Database manager initialized successfully
2025-05-20 08:03:11 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:03:11 [INFO] Webcam manager initialized
2025-05-20 08:03:11 [INFO] Webcam: Starting webcam...
2025-05-20 08:03:12 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:03:12 [INFO] Webcam started successfully
2025-05-20 08:03:12 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:03:12 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:03:12 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 08:03:12 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:03:12 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:03:12 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:03:12 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:03:12 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:03:12 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:03:12 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:03:12 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:03:12 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:03:12 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:03:12 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:03:12 [INFO] Added webcam feed control with default photo
2025-05-20 08:03:12 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:03:15 [INFO] Help content loaded from file
2025-05-20 08:03:15 [WARNING] Error resizing help dialog: This value of type "Gui" has no property named "Controls".
2025-05-20 08:03:59 [INFO] Help dialog closed
2025-05-20 08:05:16 [INFO] Application exiting: Single (Code: 0)
2025-05-20 08:05:16 [INFO] Error handler initialized
2025-05-20 08:05:16 [INFO] Read database path from config: db
2025-05-20 08:05:16 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:05:16 [INFO] Error handler initialized
2025-05-20 08:05:16 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:05:16 [INFO] PathManager initialized successfully
2025-05-20 08:05:16 [INFO] PathManager initialized successfully
2025-05-20 08:05:16 [INFO] Validating required files and directories
2025-05-20 08:05:16 [INFO] Validated directory: Database
2025-05-20 08:05:16 [INFO] Validated directory: Images
2025-05-20 08:05:16 [INFO] Validated directory: Logs
2025-05-20 08:05:16 [INFO] Validated directory: Temporary files
2025-05-20 08:05:16 [INFO] Validated directory: Candidate images
2025-05-20 08:05:16 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:05:16 [INFO] All required files and directories validated successfully
2025-05-20 08:05:16 [INFO] Initializing application components
2025-05-20 08:05:16 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:05:16 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:05:16 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:05:16 [INFO] Room cache loaded with 3 entries
2025-05-20 08:05:16 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:05:16 [INFO] Loaded 1 seat assignments
2025-05-20 08:05:16 [INFO] Database manager initialized successfully
2025-05-20 08:05:16 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:05:16 [INFO] Webcam manager initialized
2025-05-20 08:05:16 [INFO] Webcam: Starting webcam...
2025-05-20 08:05:17 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:05:17 [INFO] Webcam started successfully
2025-05-20 08:05:17 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:05:17 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:05:17 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 08:05:17 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:05:17 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:05:17 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:05:17 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:05:17 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:05:17 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:05:17 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:05:17 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:05:17 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:05:17 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:05:17 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:05:17 [INFO] Added webcam feed control with default photo
2025-05-20 08:05:17 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:05:20 [INFO] Help content loaded from file
2025-05-20 08:05:20 [WARNING] Error resizing help dialog: This value of type "Gui" has no property named "Controls".
2025-05-20 08:05:28 [INFO] Help dialog closed
2025-05-20 08:06:30 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:06:32 [INFO] Error handler initialized
2025-05-20 08:06:32 [INFO] Read database path from config: db
2025-05-20 08:06:32 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:06:32 [INFO] Error handler initialized
2025-05-20 08:06:32 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:06:32 [INFO] PathManager initialized successfully
2025-05-20 08:06:32 [INFO] PathManager initialized successfully
2025-05-20 08:06:32 [INFO] Validating required files and directories
2025-05-20 08:06:32 [INFO] Validated directory: Database
2025-05-20 08:06:32 [INFO] Validated directory: Images
2025-05-20 08:06:32 [INFO] Validated directory: Logs
2025-05-20 08:06:32 [INFO] Validated directory: Temporary files
2025-05-20 08:06:32 [INFO] Validated directory: Candidate images
2025-05-20 08:06:32 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:06:32 [INFO] All required files and directories validated successfully
2025-05-20 08:06:32 [INFO] Initializing application components
2025-05-20 08:06:32 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:06:32 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:06:32 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:06:32 [INFO] Room cache loaded with 3 entries
2025-05-20 08:06:32 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:06:32 [INFO] Loaded 1 seat assignments
2025-05-20 08:06:32 [INFO] Database manager initialized successfully
2025-05-20 08:06:32 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:06:32 [INFO] Webcam manager initialized
2025-05-20 08:06:32 [INFO] Webcam: Starting webcam...
2025-05-20 08:06:33 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:06:33 [INFO] Webcam started successfully
2025-05-20 08:06:33 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:06:33 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:06:33 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 08:06:33 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:06:33 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:06:33 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:06:33 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:06:33 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:06:33 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:06:33 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:06:33 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:06:33 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:06:33 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:06:33 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:06:33 [INFO] Added webcam feed control with default photo
2025-05-20 08:06:33 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:06:36 [INFO] Help content loaded from file
2025-05-20 08:06:36 [WARNING] Error resizing help dialog: This value of type "Gui" has no property named "Controls".
2025-05-20 08:06:52 [INFO] Help dialog closed
2025-05-20 08:06:54 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:08:13 [INFO] Error handler initialized
2025-05-20 08:08:13 [INFO] Read database path from config: db
2025-05-20 08:08:13 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:08:13 [INFO] Error handler initialized
2025-05-20 08:08:13 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:08:13 [INFO] PathManager initialized successfully
2025-05-20 08:08:13 [INFO] PathManager initialized successfully
2025-05-20 08:08:13 [INFO] Validating required files and directories
2025-05-20 08:08:13 [INFO] Validated directory: Database
2025-05-20 08:08:13 [INFO] Validated directory: Images
2025-05-20 08:08:13 [INFO] Validated directory: Logs
2025-05-20 08:08:13 [INFO] Validated directory: Temporary files
2025-05-20 08:08:13 [INFO] Validated directory: Candidate images
2025-05-20 08:08:13 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:08:13 [INFO] All required files and directories validated successfully
2025-05-20 08:08:13 [INFO] Initializing application components
2025-05-20 08:08:13 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:08:13 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:08:13 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:08:13 [INFO] Room cache loaded with 3 entries
2025-05-20 08:08:13 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:08:13 [INFO] Loaded 1 seat assignments
2025-05-20 08:08:13 [INFO] Database manager initialized successfully
2025-05-20 08:08:13 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:08:13 [INFO] Webcam manager initialized
2025-05-20 08:08:13 [INFO] Webcam: Starting webcam...
2025-05-20 08:08:13 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:08:13 [INFO] Webcam started successfully
2025-05-20 08:08:14 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:08:14 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:08:14 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 08:08:14 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:08:14 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:08:14 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:08:14 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:08:14 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:08:14 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:08:14 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:08:14 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:08:14 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:08:14 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:08:14 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:08:14 [INFO] Added webcam feed control with default photo
2025-05-20 08:08:14 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:13:11 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:16:50 [INFO] Error handler initialized
2025-05-20 08:16:50 [INFO] Read database path from config: db
2025-05-20 08:16:50 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:16:50 [INFO] Error handler initialized
2025-05-20 08:16:50 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:16:50 [INFO] PathManager initialized successfully
2025-05-20 08:16:50 [INFO] PathManager initialized successfully
2025-05-20 08:16:50 [INFO] Validating required files and directories
2025-05-20 08:16:50 [INFO] Validated directory: Database
2025-05-20 08:16:50 [INFO] Validated directory: Images
2025-05-20 08:16:50 [INFO] Validated directory: Logs
2025-05-20 08:16:50 [INFO] Validated directory: Temporary files
2025-05-20 08:16:50 [INFO] Validated directory: Candidate images
2025-05-20 08:16:50 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:16:50 [INFO] All required files and directories validated successfully
2025-05-20 08:16:50 [INFO] Initializing application components
2025-05-20 08:16:50 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:16:50 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:16:50 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:16:50 [INFO] Room cache loaded with 3 entries
2025-05-20 08:16:50 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:16:50 [INFO] Loaded 1 seat assignments
2025-05-20 08:16:50 [INFO] Database manager initialized successfully
2025-05-20 08:16:50 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:16:50 [INFO] Webcam manager initialized
2025-05-20 08:16:50 [INFO] Webcam: Starting webcam...
2025-05-20 08:16:51 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:16:51 [INFO] Webcam started successfully
2025-05-20 08:16:51 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:16:51 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:16:51 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 08:16:51 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:16:51 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:16:51 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:16:51 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:16:51 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:16:51 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:16:51 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:16:51 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:16:51 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:16:51 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:16:51 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:16:51 [INFO] Added webcam feed control with default photo
2025-05-20 08:16:51 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:17:02 [INFO] Post-Exam Mode enabled by user
2025-05-20 08:17:10 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:17:12 [INFO] Error handler initialized
2025-05-20 08:17:12 [INFO] Read database path from config: db
2025-05-20 08:17:12 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:17:12 [INFO] Error handler initialized
2025-05-20 08:17:12 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:17:12 [INFO] PathManager initialized successfully
2025-05-20 08:17:12 [INFO] PathManager initialized successfully
2025-05-20 08:17:12 [INFO] Validating required files and directories
2025-05-20 08:17:12 [INFO] Validated directory: Database
2025-05-20 08:17:12 [INFO] Validated directory: Images
2025-05-20 08:17:12 [INFO] Validated directory: Logs
2025-05-20 08:17:12 [INFO] Validated directory: Temporary files
2025-05-20 08:17:12 [INFO] Validated directory: Candidate images
2025-05-20 08:17:12 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:17:12 [INFO] All required files and directories validated successfully
2025-05-20 08:17:12 [INFO] Initializing application components
2025-05-20 08:17:12 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:17:12 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:17:12 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:17:12 [INFO] Room cache loaded with 3 entries
2025-05-20 08:17:12 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:17:12 [INFO] Loaded 1 seat assignments
2025-05-20 08:17:12 [INFO] Database manager initialized successfully
2025-05-20 08:17:12 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:17:12 [INFO] Webcam manager initialized
2025-05-20 08:17:12 [INFO] Webcam: Starting webcam...
2025-05-20 08:17:13 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:17:13 [INFO] Webcam started successfully
2025-05-20 08:17:13 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:17:13 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:17:13 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 08:17:13 [INFO] Post-exam mode is enabled
2025-05-20 08:17:13 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:17:13 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:17:13 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:17:13 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:17:13 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:17:13 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:17:13 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:17:13 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:17:13 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:17:13 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:17:13 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:17:13 [INFO] Added webcam feed control with default photo
2025-05-20 08:17:13 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:17:27 [INFO] Post-Exam Mode disabled by user
2025-05-20 08:19:34 [INFO] Application exiting: Single (Code: 0)
2025-05-20 08:19:34 [INFO] Error handler initialized
2025-05-20 08:19:34 [INFO] Read database path from config: db
2025-05-20 08:19:34 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:19:34 [INFO] Error handler initialized
2025-05-20 08:19:34 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:19:34 [INFO] PathManager initialized successfully
2025-05-20 08:19:34 [INFO] PathManager initialized successfully
2025-05-20 08:19:34 [INFO] Validating required files and directories
2025-05-20 08:19:34 [INFO] Validated directory: Database
2025-05-20 08:19:34 [INFO] Validated directory: Images
2025-05-20 08:19:34 [INFO] Validated directory: Logs
2025-05-20 08:19:34 [INFO] Validated directory: Temporary files
2025-05-20 08:19:34 [INFO] Validated directory: Candidate images
2025-05-20 08:19:34 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:19:34 [INFO] All required files and directories validated successfully
2025-05-20 08:19:34 [INFO] Initializing application components
2025-05-20 08:19:34 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:19:34 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:19:34 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:19:34 [INFO] Room cache loaded with 3 entries
2025-05-20 08:19:34 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:19:34 [INFO] Loaded 1 seat assignments
2025-05-20 08:19:34 [INFO] Database manager initialized successfully
2025-05-20 08:19:34 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:19:34 [INFO] Webcam manager initialized
2025-05-20 08:19:34 [INFO] Webcam: Starting webcam...
2025-05-20 08:19:35 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:19:35 [INFO] Webcam started successfully
2025-05-20 08:19:35 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:19:35 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:19:35 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 08:19:35 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:19:35 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:19:35 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:19:35 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:19:35 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:19:35 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:19:35 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:19:35 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:19:35 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:19:35 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:19:35 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:19:35 [INFO] Added webcam feed control with default photo
2025-05-20 08:19:35 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:19:50 [INFO] Post-Exam Mode toggle attempted but master control is disabled
2025-05-20 08:24:00 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:24:02 [INFO] Error handler initialized
2025-05-20 08:24:02 [INFO] Read database path from config: db
2025-05-20 08:24:03 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:24:03 [INFO] Error handler initialized
2025-05-20 08:24:03 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:24:03 [INFO] PathManager initialized successfully
2025-05-20 08:24:03 [INFO] PathManager initialized successfully
2025-05-20 08:24:03 [INFO] Validating required files and directories
2025-05-20 08:24:03 [INFO] Validated directory: Database
2025-05-20 08:24:03 [INFO] Validated directory: Images
2025-05-20 08:24:03 [INFO] Validated directory: Logs
2025-05-20 08:24:03 [INFO] Validated directory: Temporary files
2025-05-20 08:24:03 [INFO] Validated directory: Candidate images
2025-05-20 08:24:03 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:24:03 [INFO] All required files and directories validated successfully
2025-05-20 08:24:03 [INFO] Initializing application components
2025-05-20 08:24:03 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:24:03 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:24:03 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:24:03 [INFO] Room cache loaded with 3 entries
2025-05-20 08:24:03 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:24:03 [INFO] Loaded 1 seat assignments
2025-05-20 08:24:03 [INFO] Database manager initialized successfully
2025-05-20 08:24:03 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:24:03 [INFO] Webcam manager initialized
2025-05-20 08:24:03 [INFO] Webcam: Starting webcam...
2025-05-20 08:24:03 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:24:03 [INFO] Webcam started successfully
2025-05-20 08:24:03 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:24:03 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:24:03 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 08:24:03 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:24:04 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:24:04 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:24:04 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:24:04 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:24:04 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:24:04 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:24:04 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:24:04 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:24:04 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:24:04 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:24:04 [INFO] Added webcam feed control with default photo
2025-05-20 08:24:04 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:24:11 [INFO] Post-Exam Mode toggle attempted but master control is disabled
2025-05-20 08:24:41 [INFO] Post-Exam Mode enabled by user
2025-05-20 08:25:53 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:25:59 [INFO] Error handler initialized
2025-05-20 08:25:59 [INFO] Read database path from config: db
2025-05-20 08:25:59 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:25:59 [INFO] Error handler initialized
2025-05-20 08:25:59 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:25:59 [INFO] PathManager initialized successfully
2025-05-20 08:25:59 [INFO] PathManager initialized successfully
2025-05-20 08:25:59 [INFO] Validating required files and directories
2025-05-20 08:25:59 [INFO] Validated directory: Database
2025-05-20 08:25:59 [INFO] Validated directory: Images
2025-05-20 08:25:59 [INFO] Validated directory: Logs
2025-05-20 08:25:59 [INFO] Validated directory: Temporary files
2025-05-20 08:25:59 [INFO] Validated directory: Candidate images
2025-05-20 08:25:59 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:25:59 [INFO] All required files and directories validated successfully
2025-05-20 08:25:59 [INFO] Initializing application components
2025-05-20 08:25:59 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:25:59 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:25:59 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:25:59 [INFO] Room cache loaded with 3 entries
2025-05-20 08:25:59 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:25:59 [INFO] Loaded 1 seat assignments
2025-05-20 08:25:59 [INFO] Database manager initialized successfully
2025-05-20 08:25:59 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:25:59 [INFO] Webcam manager initialized
2025-05-20 08:25:59 [INFO] Webcam: Starting webcam...
2025-05-20 08:25:59 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:25:59 [INFO] Webcam started successfully
2025-05-20 08:25:59 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:25:59 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:25:59 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 08:25:59 [INFO] Post-exam mode is enabled
2025-05-20 08:25:59 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:25:59 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:25:59 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:25:59 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:25:59 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:25:59 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:25:59 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:25:59 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:25:59 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:25:59 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:25:59 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:25:59 [INFO] Added webcam feed control with default photo
2025-05-20 08:26:00 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:28:17 [INFO] Post-Exam Mode disabled by user
2025-05-20 08:28:46 [INFO] Post-Exam Mode enabled by user
2025-05-20 08:30:02 [INFO] Found seat assignment in cache for 9351: F1-R1-S5
2025-05-20 08:30:54 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:30:57 [INFO] Error handler initialized
2025-05-20 08:30:57 [INFO] Read database path from config: db
2025-05-20 08:30:57 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:30:57 [INFO] Error handler initialized
2025-05-20 08:30:57 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:30:57 [INFO] PathManager initialized successfully
2025-05-20 08:30:57 [INFO] PathManager initialized successfully
2025-05-20 08:30:57 [INFO] Validating required files and directories
2025-05-20 08:30:57 [INFO] Validated directory: Database
2025-05-20 08:30:57 [INFO] Validated directory: Images
2025-05-20 08:30:57 [INFO] Validated directory: Logs
2025-05-20 08:30:57 [INFO] Validated directory: Temporary files
2025-05-20 08:30:57 [INFO] Validated directory: Candidate images
2025-05-20 08:30:57 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:30:57 [INFO] All required files and directories validated successfully
2025-05-20 08:30:57 [INFO] Initializing application components
2025-05-20 08:30:57 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:30:57 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:30:57 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:30:57 [INFO] Room cache loaded with 3 entries
2025-05-20 08:30:57 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:30:57 [INFO] Loaded 1 seat assignments
2025-05-20 08:30:57 [INFO] Database manager initialized successfully
2025-05-20 08:30:57 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:30:57 [INFO] Webcam manager initialized
2025-05-20 08:30:57 [INFO] Webcam: Starting webcam...
2025-05-20 08:30:58 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:30:58 [INFO] Webcam started successfully
2025-05-20 08:30:58 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:30:58 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:30:58 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 08:30:58 [ERROR] Error updating Post-Exam Mode indicators: This value of type "String" has no property named "Title".
2025-05-20 08:30:58 [INFO] Post-exam mode is enabled
2025-05-20 08:30:58 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:30:58 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:30:58 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:30:58 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:30:58 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:30:58 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:30:58 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:30:58 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:30:58 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:30:58 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:30:58 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:30:58 [INFO] Added webcam feed control with default photo
2025-05-20 08:30:58 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:31:06 [INFO] Post-Exam Mode indicators updated - Mode is disabled
2025-05-20 08:31:06 [ERROR] Error updating Post-Exam Mode indicators: Invalid option.
2025-05-20 08:31:06 [INFO] Post-Exam Mode disabled by user
2025-05-20 08:31:18 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:31:19 [INFO] Error handler initialized
2025-05-20 08:31:19 [INFO] Read database path from config: db
2025-05-20 08:31:19 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:31:19 [INFO] Error handler initialized
2025-05-20 08:31:19 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:31:19 [INFO] PathManager initialized successfully
2025-05-20 08:31:19 [INFO] PathManager initialized successfully
2025-05-20 08:31:19 [INFO] Validating required files and directories
2025-05-20 08:31:19 [INFO] Validated directory: Database
2025-05-20 08:31:19 [INFO] Validated directory: Images
2025-05-20 08:31:19 [INFO] Validated directory: Logs
2025-05-20 08:31:19 [INFO] Validated directory: Temporary files
2025-05-20 08:31:19 [INFO] Validated directory: Candidate images
2025-05-20 08:31:19 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:31:19 [INFO] All required files and directories validated successfully
2025-05-20 08:31:19 [INFO] Initializing application components
2025-05-20 08:31:19 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:31:19 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:31:19 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:31:19 [INFO] Room cache loaded with 3 entries
2025-05-20 08:31:19 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:31:19 [INFO] Loaded 1 seat assignments
2025-05-20 08:31:19 [INFO] Database manager initialized successfully
2025-05-20 08:31:19 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:31:19 [INFO] Webcam manager initialized
2025-05-20 08:31:19 [INFO] Webcam: Starting webcam...
2025-05-20 08:31:20 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:31:20 [INFO] Webcam started successfully
2025-05-20 08:31:20 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:31:20 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:31:20 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 08:31:20 [ERROR] Error updating Post-Exam Mode indicators: This value of type "String" has no property named "Title".
2025-05-20 08:31:20 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:31:20 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:31:20 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:31:20 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:31:20 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:31:20 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:31:20 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:31:20 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:31:20 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:31:20 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:31:20 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:31:20 [INFO] Added webcam feed control with default photo
2025-05-20 08:31:20 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:37:46 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:39:12 [INFO] Error handler initialized
2025-05-20 08:39:12 [INFO] Read database path from config: db
2025-05-20 08:39:12 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:39:12 [INFO] Error handler initialized
2025-05-20 08:39:12 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:39:12 [INFO] PathManager initialized successfully
2025-05-20 08:39:12 [INFO] PathManager initialized successfully
2025-05-20 08:39:12 [INFO] Validating required files and directories
2025-05-20 08:39:12 [INFO] Validated directory: Database
2025-05-20 08:39:12 [INFO] Validated directory: Images
2025-05-20 08:39:12 [INFO] Validated directory: Logs
2025-05-20 08:39:12 [INFO] Validated directory: Temporary files
2025-05-20 08:39:12 [INFO] Validated directory: Candidate images
2025-05-20 08:39:12 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:39:12 [INFO] All required files and directories validated successfully
2025-05-20 08:39:12 [INFO] Initializing application components
2025-05-20 08:39:12 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:39:13 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:39:13 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:39:13 [INFO] Room cache loaded with 3 entries
2025-05-20 08:39:13 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:39:13 [INFO] Loaded 1 seat assignments
2025-05-20 08:39:13 [INFO] Database manager initialized successfully
2025-05-20 08:39:13 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:39:13 [INFO] Webcam manager initialized
2025-05-20 08:39:13 [INFO] Webcam: Starting webcam...
2025-05-20 08:39:13 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:39:13 [INFO] Webcam started successfully
2025-05-20 08:39:13 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:39:13 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:39:13 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 08:39:32 [ERROR] Error updating Post-Exam Mode indicators: This value of type "String" has no method named "Opt".
2025-05-20 08:39:32 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:39:32 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:39:32 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:39:32 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:39:32 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:39:32 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:39:32 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:39:32 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:39:32 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:39:32 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:39:32 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:39:32 [INFO] Added webcam feed control with default photo
2025-05-20 08:39:32 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:39:42 [ERROR] Error toggling Post-Exam Mode: Invalid option.
2025-05-20 08:40:19 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:40:27 [INFO] Error handler initialized
2025-05-20 08:40:27 [INFO] Read database path from config: db
2025-05-20 08:40:27 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:40:27 [INFO] Error handler initialized
2025-05-20 08:40:27 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:40:27 [INFO] PathManager initialized successfully
2025-05-20 08:40:27 [INFO] PathManager initialized successfully
2025-05-20 08:40:27 [INFO] Validating required files and directories
2025-05-20 08:40:27 [INFO] Validated directory: Database
2025-05-20 08:40:27 [INFO] Validated directory: Images
2025-05-20 08:40:27 [INFO] Validated directory: Logs
2025-05-20 08:40:27 [INFO] Validated directory: Temporary files
2025-05-20 08:40:27 [INFO] Validated directory: Candidate images
2025-05-20 08:40:27 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:40:27 [INFO] All required files and directories validated successfully
2025-05-20 08:40:27 [INFO] Initializing application components
2025-05-20 08:40:27 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:40:27 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:40:27 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:40:27 [INFO] Room cache loaded with 3 entries
2025-05-20 08:40:27 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:40:27 [INFO] Loaded 1 seat assignments
2025-05-20 08:40:27 [INFO] Database manager initialized successfully
2025-05-20 08:40:27 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:40:27 [INFO] Webcam manager initialized
2025-05-20 08:40:27 [INFO] Webcam: Starting webcam...
2025-05-20 08:40:28 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:40:28 [INFO] Webcam started successfully
2025-05-20 08:40:28 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:40:28 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:40:28 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 08:40:28 [ERROR] Error updating Post-Exam Mode indicators: This global variable has not been assigned a value.
2025-05-20 08:40:28 [INFO] Post-exam mode is enabled
2025-05-20 08:40:28 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:40:28 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:40:28 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:40:28 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:40:28 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:40:28 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:40:28 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:40:28 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:40:28 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:40:28 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:40:28 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:40:28 [INFO] Added webcam feed control with default photo
2025-05-20 08:40:28 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:40:43 [ERROR] Error updating Post-Exam Mode indicators: Invalid option.
2025-05-20 08:40:43 [INFO] Post-Exam Mode disabled by user
2025-05-20 08:40:44 [ERROR] Error updating Post-Exam Mode indicators: Invalid option.
2025-05-20 08:40:47 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:41:39 [INFO] Error handler initialized
2025-05-20 08:41:39 [INFO] Read database path from config: db
2025-05-20 08:41:39 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:41:39 [INFO] Error handler initialized
2025-05-20 08:41:39 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:41:39 [INFO] PathManager initialized successfully
2025-05-20 08:41:39 [INFO] PathManager initialized successfully
2025-05-20 08:41:39 [INFO] Validating required files and directories
2025-05-20 08:41:39 [INFO] Validated directory: Database
2025-05-20 08:41:39 [INFO] Validated directory: Images
2025-05-20 08:41:39 [INFO] Validated directory: Logs
2025-05-20 08:41:39 [INFO] Validated directory: Temporary files
2025-05-20 08:41:39 [INFO] Validated directory: Candidate images
2025-05-20 08:41:39 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:41:39 [INFO] All required files and directories validated successfully
2025-05-20 08:41:39 [INFO] Initializing application components
2025-05-20 08:41:39 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:41:39 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:41:39 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:41:39 [INFO] Room cache loaded with 3 entries
2025-05-20 08:41:39 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:41:39 [INFO] Loaded 1 seat assignments
2025-05-20 08:41:39 [INFO] Database manager initialized successfully
2025-05-20 08:41:39 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:41:39 [INFO] Webcam manager initialized
2025-05-20 08:41:39 [INFO] Webcam: Starting webcam...
2025-05-20 08:41:39 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:41:39 [INFO] Webcam started successfully
2025-05-20 08:41:40 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:41:40 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:41:40 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 08:41:40 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:41:40 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:41:40 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:41:40 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:41:40 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:41:40 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:41:40 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:41:40 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:41:40 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:41:40 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:41:40 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:41:40 [INFO] Added webcam feed control with default photo
2025-05-20 08:41:40 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:41:48 [INFO] Post-Exam Mode enabled by user
2025-05-20 08:42:36 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:51:16 [INFO] Error handler initialized
2025-05-20 08:51:16 [INFO] Read database path from config: db
2025-05-20 08:51:16 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:51:16 [INFO] Error handler initialized
2025-05-20 08:51:16 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:51:16 [INFO] Validated directory: Temporary files
2025-05-20 08:51:16 [INFO] Validated directory: Candidate images
2025-05-20 08:51:16 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:51:16 [INFO] All required files and directories validated successfully
2025-05-20 08:51:16 [INFO] Initializing application components
2025-05-20 08:51:16 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:51:16 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:51:16 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:51:16 [INFO] Room cache loaded with 3 entries
2025-05-20 08:51:16 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:51:16 [INFO] Loaded 1 seat assignments
2025-05-20 08:51:16 [INFO] Database manager initialized successfully
2025-05-20 08:51:16 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:51:16 [INFO] Webcam manager initialized
2025-05-20 08:51:16 [INFO] Webcam: Starting webcam...
2025-05-20 08:51:17 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:51:17 [INFO] Webcam started successfully
2025-05-20 08:51:17 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:51:17 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:51:17 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 08:51:17 [INFO] Post-exam mode is enabled
2025-05-20 08:51:17 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:51:17 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:51:17 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:51:17 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:51:17 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:51:17 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:51:17 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:51:17 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:51:17 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:51:17 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:51:17 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:51:17 [INFO] Added webcam feed control with default photo
2025-05-20 08:51:18 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:51:28 [INFO] Post-Exam Mode disabled by user
2025-05-20 08:51:37 [INFO] Post-Exam Mode enabled by user
2025-05-20 08:52:19 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 08:53:22 [INFO] Error handler initialized
2025-05-20 08:53:22 [INFO] Read database path from config: db
2025-05-20 08:53:22 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 08:53:22 [INFO] Error handler initialized
2025-05-20 08:53:22 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 08:53:22 [INFO] PathManager initialized successfully
2025-05-20 08:53:22 [INFO] PathManager initialized successfully
2025-05-20 08:53:22 [INFO] Validating required files and directories
2025-05-20 08:53:22 [INFO] Validated directory: Database
2025-05-20 08:53:22 [INFO] Validated directory: Images
2025-05-20 08:53:22 [INFO] Validated directory: Logs
2025-05-20 08:53:22 [INFO] Validated directory: Temporary files
2025-05-20 08:53:22 [INFO] Validated directory: Candidate images
2025-05-20 08:53:22 [INFO] Validated directory: Fingerprint templates
2025-05-20 08:53:22 [INFO] All required files and directories validated successfully
2025-05-20 08:53:22 [INFO] Initializing application components
2025-05-20 08:53:22 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 08:53:22 [INFO] Hardware cache loaded with 10 entries
2025-05-20 08:53:22 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 08:53:22 [INFO] Room cache loaded with 3 entries
2025-05-20 08:53:22 [INFO] Loading seat assignments from seat ID sections
2025-05-20 08:53:23 [INFO] Loaded 1 seat assignments
2025-05-20 08:53:23 [INFO] Database manager initialized successfully
2025-05-20 08:53:23 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 08:53:23 [INFO] Webcam manager initialized
2025-05-20 08:53:23 [INFO] Webcam: Starting webcam...
2025-05-20 08:53:23 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 08:53:23 [INFO] Webcam started successfully
2025-05-20 08:53:23 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 08:53:23 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 08:53:23 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 08:53:23 [INFO] Post-exam mode is enabled
2025-05-20 08:53:23 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 08:53:23 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 08:53:23 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 08:53:23 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 08:53:23 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 08:53:23 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 08:53:23 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 08:53:23 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 08:53:23 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 08:53:23 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 08:53:23 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 08:53:24 [INFO] Added webcam feed control with default photo
2025-05-20 08:53:24 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 08:53:26 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 12:06:00 [INFO] Error handler initialized
2025-05-20 12:06:00 [INFO] Read database path from config: db
2025-05-20 12:06:00 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 12:06:00 [INFO] Error handler initialized
2025-05-20 12:06:00 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 12:06:00 [INFO] PathManager initialized successfully
2025-05-20 12:06:00 [INFO] PathManager initialized successfully
2025-05-20 12:06:00 [INFO] Validating required files and directories
2025-05-20 12:06:00 [INFO] Validated directory: Database
2025-05-20 12:06:00 [INFO] Validated directory: Images
2025-05-20 12:06:00 [INFO] Validated directory: Logs
2025-05-20 12:06:00 [INFO] Validated directory: Temporary files
2025-05-20 12:06:00 [INFO] Validated directory: Candidate images
2025-05-20 12:06:00 [INFO] Validated directory: Fingerprint templates
2025-05-20 12:06:00 [INFO] All required files and directories validated successfully
2025-05-20 12:06:00 [INFO] Initializing application components
2025-05-20 12:06:00 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 12:06:00 [INFO] Hardware cache loaded with 10 entries
2025-05-20 12:06:00 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 12:06:00 [INFO] Room cache loaded with 3 entries
2025-05-20 12:06:00 [INFO] Loading seat assignments from seat ID sections
2025-05-20 12:06:00 [INFO] Loaded 1 seat assignments
2025-05-20 12:06:00 [INFO] Database manager initialized successfully
2025-05-20 12:06:00 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 12:06:00 [INFO] Webcam manager initialized
2025-05-20 12:06:00 [INFO] Webcam: Starting webcam...
2025-05-20 12:06:01 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 12:06:01 [INFO] Webcam started successfully
2025-05-20 12:06:01 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 12:06:01 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 12:06:01 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-20 12:06:01 [INFO] Post-exam mode is enabled
2025-05-20 12:06:01 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 12:06:01 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 12:06:01 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 12:06:01 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 12:06:01 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 12:06:01 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 12:06:01 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 12:06:01 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 12:06:01 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 12:06:01 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 12:06:01 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 12:06:02 [INFO] Added webcam feed control with default photo
2025-05-20 12:06:02 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 12:07:19 [INFO] Post-Exam Mode disabled by user
2025-05-20 12:07:30 [INFO] No seat assignment found for 9353
2025-05-20 12:07:36 [INFO] No seat assignment found for 9355
2025-05-20 12:07:57 [INFO] No seat assignment found for 9355
2025-05-20 12:08:15 [INFO] No seat assignment found for 9355
2025-05-20 12:08:51 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 12:08:51 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-20 12:08:51 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 12:08:51 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 12:08:51 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 12:08:51 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 12:08:53 [INFO] No seat assignment found for 9355
2025-05-20 12:09:03 [INFO] No seat assignment found for 9355
2025-05-20 12:09:10 [INFO] No seat assignment found for 9355
2025-05-20 12:10:19 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 12:10:19 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 12:10:19 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 12:10:19 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 12:10:19 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 12:10:19 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 12:10:21 [INFO] No seat assignment found for 9355
2025-05-20 12:16:53 [INFO] Application exiting: Reload (Code: 0)
2025-05-20 12:16:53 [INFO] Error handler initialized
2025-05-20 12:16:53 [INFO] Read database path from config: db
2025-05-20 12:16:53 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 12:16:53 [INFO] Error handler initialized
2025-05-20 12:16:53 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 12:16:53 [INFO] PathManager initialized successfully
2025-05-20 12:16:53 [INFO] PathManager initialized successfully
2025-05-20 12:16:53 [INFO] Validating required files and directories
2025-05-20 12:16:53 [INFO] Validated directory: Database
2025-05-20 12:16:53 [INFO] Validated directory: Images
2025-05-20 12:16:53 [INFO] Validated directory: Logs
2025-05-20 12:16:53 [INFO] Validated directory: Temporary files
2025-05-20 12:16:53 [INFO] Validated directory: Candidate images
2025-05-20 12:16:53 [INFO] Validated directory: Fingerprint templates
2025-05-20 12:16:53 [INFO] All required files and directories validated successfully
2025-05-20 12:16:53 [INFO] Initializing application components
2025-05-20 12:16:53 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 12:16:53 [INFO] Hardware cache loaded with 10 entries
2025-05-20 12:16:53 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 12:16:53 [INFO] Room cache loaded with 3 entries
2025-05-20 12:16:53 [INFO] Loading seat assignments from seat ID sections
2025-05-20 12:16:53 [INFO] Loaded 1 seat assignments
2025-05-20 12:16:53 [INFO] Database manager initialized successfully
2025-05-20 12:16:53 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 12:16:53 [INFO] Webcam manager initialized
2025-05-20 12:16:53 [INFO] Webcam: Starting webcam...
2025-05-20 12:16:54 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 12:16:54 [INFO] Webcam started successfully
2025-05-20 12:16:54 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 12:16:54 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 12:16:54 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 12:16:54 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 12:16:54 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 12:16:54 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 12:16:54 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 12:16:54 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 12:16:54 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 12:16:54 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 12:16:54 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 12:16:54 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 12:16:54 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 12:16:54 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 12:16:54 [INFO] Added webcam feed control with default photo
2025-05-20 12:16:54 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 12:17:36 [INFO] Application exiting: Single (Code: 0)
2025-05-20 12:17:36 [INFO] Error handler initialized
2025-05-20 12:17:36 [INFO] Read database path from config: db
2025-05-20 12:17:36 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 12:17:36 [INFO] Error handler initialized
2025-05-20 12:17:36 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 12:17:36 [INFO] PathManager initialized successfully
2025-05-20 12:17:36 [INFO] PathManager initialized successfully
2025-05-20 12:17:36 [INFO] Validating required files and directories
2025-05-20 12:17:36 [INFO] Validated directory: Database
2025-05-20 12:17:36 [INFO] Validated directory: Images
2025-05-20 12:17:36 [INFO] Validated directory: Logs
2025-05-20 12:17:36 [INFO] Validated directory: Temporary files
2025-05-20 12:17:36 [INFO] Validated directory: Candidate images
2025-05-20 12:17:36 [INFO] Validated directory: Fingerprint templates
2025-05-20 12:17:36 [INFO] All required files and directories validated successfully
2025-05-20 12:17:36 [INFO] Initializing application components
2025-05-20 12:17:37 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 12:17:37 [INFO] Hardware cache loaded with 10 entries
2025-05-20 12:17:37 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 12:17:37 [INFO] Room cache loaded with 3 entries
2025-05-20 12:17:37 [INFO] Loading seat assignments from seat ID sections
2025-05-20 12:17:37 [INFO] Loaded 1 seat assignments
2025-05-20 12:17:37 [INFO] Database manager initialized successfully
2025-05-20 12:17:37 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 12:17:37 [INFO] Webcam manager initialized
2025-05-20 12:17:37 [INFO] Webcam: Starting webcam...
2025-05-20 12:17:37 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 12:17:37 [INFO] Webcam started successfully
2025-05-20 12:17:37 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 12:17:37 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 12:17:37 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 12:17:37 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 12:17:37 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 12:17:37 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 12:17:37 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 12:17:37 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 12:17:37 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 12:17:37 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 12:17:37 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 12:17:37 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 12:17:37 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 12:17:37 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 12:17:37 [INFO] Added webcam feed control with default photo
2025-05-20 12:17:38 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 12:19:29 [INFO] Application exiting: Single (Code: 0)
2025-05-20 12:19:29 [INFO] Error handler initialized
2025-05-20 12:19:29 [INFO] Read database path from config: db
2025-05-20 12:19:29 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 12:19:29 [INFO] Error handler initialized
2025-05-20 12:19:29 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 12:19:29 [INFO] PathManager initialized successfully
2025-05-20 12:19:29 [INFO] PathManager initialized successfully
2025-05-20 12:19:29 [INFO] Validating required files and directories
2025-05-20 12:19:29 [INFO] Validated directory: Database
2025-05-20 12:19:29 [INFO] Validated directory: Images
2025-05-20 12:19:29 [INFO] Validated directory: Logs
2025-05-20 12:19:29 [INFO] Validated directory: Temporary files
2025-05-20 12:19:29 [INFO] Validated directory: Candidate images
2025-05-20 12:19:29 [INFO] Validated directory: Fingerprint templates
2025-05-20 12:19:29 [INFO] All required files and directories validated successfully
2025-05-20 12:19:29 [INFO] Initializing application components
2025-05-20 12:19:29 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 12:19:29 [INFO] Hardware cache loaded with 10 entries
2025-05-20 12:19:29 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 12:19:29 [INFO] Room cache loaded with 3 entries
2025-05-20 12:19:29 [INFO] Loading seat assignments from seat ID sections
2025-05-20 12:19:30 [INFO] Loaded 1 seat assignments
2025-05-20 12:19:30 [INFO] Database manager initialized successfully
2025-05-20 12:19:30 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 12:19:30 [INFO] Webcam manager initialized
2025-05-20 12:19:30 [INFO] Webcam: Starting webcam...
2025-05-20 12:19:30 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 12:19:30 [INFO] Webcam started successfully
2025-05-20 12:19:30 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 12:19:30 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 12:19:30 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 12:19:30 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 12:19:30 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 12:19:30 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 12:19:30 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 12:19:30 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 12:19:30 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 12:19:30 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 12:19:30 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 12:19:30 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 12:19:30 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 12:19:30 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 12:19:30 [INFO] Added webcam feed control with default photo
2025-05-20 12:19:30 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 12:26:01 [INFO] Application exiting: Single (Code: 0)
2025-05-20 12:26:02 [INFO] Read database path from config: db
2025-05-20 12:26:02 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 12:26:02 [INFO] Error handler initialized
2025-05-20 12:26:02 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 12:26:02 [INFO] PathManager initialized successfully
2025-05-20 12:26:02 [INFO] PathManager initialized successfully
2025-05-20 12:26:02 [INFO] Validating required files and directories
2025-05-20 12:26:02 [INFO] Validated directory: Database
2025-05-20 12:26:02 [INFO] Validated directory: Images
2025-05-20 12:26:02 [INFO] Validated directory: Logs
2025-05-20 12:26:02 [INFO] Validated directory: Temporary files
2025-05-20 12:26:02 [INFO] Validated directory: Candidate images
2025-05-20 12:26:02 [INFO] Validated directory: Fingerprint templates
2025-05-20 12:26:02 [INFO] All required files and directories validated successfully
2025-05-20 12:26:02 [INFO] Initializing application components
2025-05-20 12:26:02 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 12:26:02 [INFO] Hardware cache loaded with 10 entries
2025-05-20 12:26:02 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 12:26:02 [INFO] Room cache loaded with 3 entries
2025-05-20 12:26:02 [INFO] Loading seat assignments from seat ID sections
2025-05-20 12:26:02 [INFO] Loaded 1 seat assignments
2025-05-20 12:26:02 [INFO] Database manager initialized successfully
2025-05-20 12:26:02 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 12:26:02 [INFO] Webcam manager initialized
2025-05-20 12:26:02 [INFO] Webcam: Starting webcam...
2025-05-20 12:26:03 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 12:26:03 [INFO] Webcam started successfully
2025-05-20 12:26:03 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 12:26:03 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 12:26:03 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 12:26:03 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 12:26:03 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 12:26:03 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 12:26:03 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 12:26:03 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 12:26:03 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 12:26:03 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 12:26:03 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 12:26:03 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 12:26:03 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 12:26:03 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 12:26:03 [INFO] Added webcam feed control with default photo
2025-05-20 12:26:03 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 12:28:06 [INFO] About dialog closed
2025-05-20 12:28:09 [INFO] Help content loaded from file
2025-05-20 12:28:09 [WARNING] Error resizing help dialog: This value of type "Gui" has no property named "Controls".
2025-05-20 12:28:35 [INFO] Help dialog closed
2025-05-20 12:28:46 [INFO] Found seat assignment in cache for 9351: F1-R1-S5
2025-05-20 12:29:13 [INFO] Application exiting: Single (Code: 0)
2025-05-20 12:29:14 [INFO] Error handler initialized
2025-05-20 12:29:14 [INFO] Read database path from config: db
2025-05-20 12:29:14 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 12:29:14 [INFO] Error handler initialized
2025-05-20 12:29:14 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 12:29:14 [INFO] PathManager initialized successfully
2025-05-20 12:29:14 [INFO] PathManager initialized successfully
2025-05-20 12:29:14 [INFO] Validating required files and directories
2025-05-20 12:29:14 [INFO] Validated directory: Database
2025-05-20 12:29:14 [INFO] Validated directory: Images
2025-05-20 12:29:14 [INFO] Validated directory: Logs
2025-05-20 12:29:14 [INFO] Validated directory: Temporary files
2025-05-20 12:29:14 [INFO] Validated directory: Candidate images
2025-05-20 12:29:14 [INFO] Validated directory: Fingerprint templates
2025-05-20 12:29:14 [INFO] All required files and directories validated successfully
2025-05-20 12:29:14 [INFO] Initializing application components
2025-05-20 12:29:14 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 12:29:14 [INFO] Hardware cache loaded with 10 entries
2025-05-20 12:29:14 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 12:29:14 [INFO] Room cache loaded with 3 entries
2025-05-20 12:29:14 [INFO] Loading seat assignments from seat ID sections
2025-05-20 12:29:14 [INFO] Loaded 0 seat assignments
2025-05-20 12:29:14 [INFO] Database manager initialized successfully
2025-05-20 12:29:14 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 12:29:14 [INFO] Webcam manager initialized
2025-05-20 12:29:14 [INFO] Webcam: Starting webcam...
2025-05-20 12:29:14 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 12:29:14 [INFO] Webcam started successfully
2025-05-20 12:29:14 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 12:29:14 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 12:29:14 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 12:29:14 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 12:29:14 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 12:29:14 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 12:29:14 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 12:29:14 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 12:29:15 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 12:29:15 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 12:29:15 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 12:29:15 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 12:29:15 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 12:29:15 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 12:29:15 [INFO] Added webcam feed control with default photo
2025-05-20 12:29:15 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 12:29:18 [INFO] No seat assignment found for 9351
2025-05-20 12:32:07 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 12:32:07 [INFO] No seat assignment found for 9351
2025-05-20 12:32:07 [DEBUG] Room Cache: 3 entries
2025-05-20 12:32:07 [DEBUG] Hardware Cache: 10 entries
2025-05-20 12:32:07 [DEBUG] Seat Assignment Cache: 0 entries
2025-05-20 12:32:11 [INFO] Seat assigned successfully for 9351: F1-R1-S8 (Special Needs allocation)
2025-05-20 12:34:21 [INFO] Found seat assignment in cache for 9351: F1-R1-S8
2025-05-20 12:34:26 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 12:34:29 [INFO] Error handler initialized
2025-05-20 12:34:29 [INFO] Read database path from config: db
2025-05-20 12:34:29 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 12:34:29 [INFO] Error handler initialized
2025-05-20 12:34:29 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 12:34:29 [INFO] PathManager initialized successfully
2025-05-20 12:34:29 [INFO] PathManager initialized successfully
2025-05-20 12:34:29 [INFO] Validating required files and directories
2025-05-20 12:34:29 [INFO] Validated directory: Database
2025-05-20 12:34:29 [INFO] Validated directory: Images
2025-05-20 12:34:29 [INFO] Validated directory: Logs
2025-05-20 12:34:29 [INFO] Validated directory: Temporary files
2025-05-20 12:34:29 [INFO] Validated directory: Candidate images
2025-05-20 12:34:29 [INFO] Validated directory: Fingerprint templates
2025-05-20 12:34:29 [INFO] All required files and directories validated successfully
2025-05-20 12:34:29 [INFO] Initializing application components
2025-05-20 12:34:29 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 12:34:29 [INFO] Hardware cache loaded with 10 entries
2025-05-20 12:34:29 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 12:34:29 [INFO] Room cache loaded with 3 entries
2025-05-20 12:34:29 [INFO] Loading seat assignments from seat ID sections
2025-05-20 12:34:30 [INFO] Loaded 0 seat assignments
2025-05-20 12:34:30 [INFO] Database manager initialized successfully
2025-05-20 12:34:30 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 12:34:30 [INFO] Webcam manager initialized
2025-05-20 12:34:30 [INFO] Webcam: Starting webcam...
2025-05-20 12:34:30 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 12:34:30 [INFO] Webcam started successfully
2025-05-20 12:34:30 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 12:34:30 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 12:34:30 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 12:34:30 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 12:34:30 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 12:34:30 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 12:34:30 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 12:34:30 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 12:34:30 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 12:34:30 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 12:34:30 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 12:34:30 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 12:34:30 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 12:34:30 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 12:34:30 [INFO] Added webcam feed control with default photo
2025-05-20 12:34:30 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 12:34:33 [INFO] No seat assignment found for 9351
2025-05-20 12:34:35 [INFO] Assigning seat for candidate: 9351 (Sanjay Kumar)
2025-05-20 12:34:35 [INFO] No seat assignment found for 9351
2025-05-20 12:34:35 [DEBUG] Room Cache: 3 entries
2025-05-20 12:34:35 [DEBUG] Hardware Cache: 10 entries
2025-05-20 12:34:35 [DEBUG] Seat Assignment Cache: 0 entries
2025-05-20 12:34:35 [INFO] Seat assigned successfully for 9351: F1-R1-S8 (Special Needs allocation)
2025-05-20 12:34:52 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 12:53:18 [INFO] Error handler initialized
2025-05-20 12:53:18 [INFO] Read database path from config: db
2025-05-20 12:53:18 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 12:53:18 [INFO] Error handler initialized
2025-05-20 12:53:18 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 12:53:18 [INFO] PathManager initialized successfully
2025-05-20 12:53:18 [INFO] PathManager initialized successfully
2025-05-20 12:53:18 [INFO] Validating required files and directories
2025-05-20 12:53:18 [INFO] Validated directory: Database
2025-05-20 12:53:18 [INFO] Validated directory: Images
2025-05-20 12:53:18 [INFO] Validated directory: Logs
2025-05-20 12:53:18 [INFO] Validated directory: Temporary files
2025-05-20 12:53:18 [INFO] Validated directory: Candidate images
2025-05-20 12:53:18 [INFO] Validated directory: Fingerprint templates
2025-05-20 12:53:18 [INFO] All required files and directories validated successfully
2025-05-20 12:53:18 [INFO] Initializing application components
2025-05-20 12:53:18 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 12:53:18 [INFO] Hardware cache loaded with 10 entries
2025-05-20 12:53:18 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 12:53:18 [INFO] Room cache loaded with 3 entries
2025-05-20 12:53:18 [INFO] Loading seat assignments from seat ID sections
2025-05-20 12:53:18 [INFO] Loaded 1 seat assignments
2025-05-20 12:53:18 [INFO] Database manager initialized successfully
2025-05-20 12:53:18 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 12:53:18 [INFO] Webcam manager initialized
2025-05-20 12:53:18 [INFO] Webcam: Starting webcam...
2025-05-20 12:53:19 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 12:53:19 [INFO] Webcam started successfully
2025-05-20 12:53:19 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 12:53:19 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 12:53:19 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 12:53:19 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 12:53:19 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 12:53:19 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 12:53:19 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 12:53:19 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 12:53:19 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 12:53:19 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 12:53:19 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 12:53:19 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 12:53:19 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 12:53:19 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 12:53:19 [INFO] Added webcam feed control with default photo
2025-05-20 12:53:19 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 12:53:38 [INFO] Found seat assignment in cache for 9351: F1-R1-S8
2025-05-20 12:54:09 [INFO] Post-Exam Mode enabled by user
2025-05-20 12:54:46 [INFO] Post-Exam Mode disabled by user
2025-05-20 12:55:14 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 12:55:14 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 12:55:14 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 12:55:14 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 12:55:14 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 12:55:14 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 12:59:18 [INFO] Application exiting: Single (Code: 0)
2025-05-20 12:59:18 [INFO] Error handler initialized
2025-05-20 12:59:18 [INFO] Read database path from config: db
2025-05-20 12:59:18 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 12:59:18 [INFO] Error handler initialized
2025-05-20 12:59:18 [INFO] Starting WinCBT-Biometric (v1.4.5 Build 20250520)
2025-05-20 12:59:18 [INFO] PathManager initialized successfully
2025-05-20 12:59:18 [INFO] PathManager initialized successfully
2025-05-20 12:59:18 [INFO] Validating required files and directories
2025-05-20 12:59:18 [INFO] Validated directory: Database
2025-05-20 12:59:18 [INFO] Validated directory: Images
2025-05-20 12:59:18 [INFO] Validated directory: Logs
2025-05-20 12:59:18 [INFO] Validated directory: Temporary files
2025-05-20 12:59:18 [INFO] Validated directory: Candidate images
2025-05-20 12:59:18 [INFO] Validated directory: Fingerprint templates
2025-05-20 12:59:18 [INFO] All required files and directories validated successfully
2025-05-20 12:59:18 [INFO] Initializing application components
2025-05-20 12:59:18 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 12:59:18 [INFO] Hardware cache loaded with 10 entries
2025-05-20 12:59:18 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 12:59:18 [INFO] Room cache loaded with 3 entries
2025-05-20 12:59:18 [INFO] Loading seat assignments from seat ID sections
2025-05-20 12:59:18 [INFO] Loaded 1 seat assignments
2025-05-20 12:59:19 [INFO] Database manager initialized successfully
2025-05-20 12:59:19 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 12:59:19 [INFO] Webcam manager initialized
2025-05-20 12:59:19 [INFO] Webcam: Starting webcam...
2025-05-20 12:59:19 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 12:59:19 [INFO] Webcam started successfully
2025-05-20 12:59:19 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 12:59:19 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 12:59:19 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 12:59:19 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 12:59:19 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 12:59:19 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 12:59:19 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 12:59:19 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 12:59:19 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 12:59:19 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 12:59:19 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 12:59:19 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 12:59:19 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 12:59:19 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 12:59:19 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 12:59:54 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 18:53:13 [INFO] Error handler initialized
2025-05-20 18:53:14 [INFO] Read database path from config: db
2025-05-20 18:53:14 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 18:53:14 [INFO] Error handler initialized
2025-05-20 18:53:14 [INFO] Starting WinCBT-Biometric (v1.4.6 Build 20250520)
2025-05-20 18:53:14 [INFO] PathManager initialized successfully
2025-05-20 18:53:14 [INFO] PathManager initialized successfully
2025-05-20 18:53:14 [INFO] Validating required files and directories
2025-05-20 18:53:14 [INFO] Validated directory: Database
2025-05-20 18:53:14 [INFO] Validated directory: Images
2025-05-20 18:53:14 [INFO] Validated directory: Logs
2025-05-20 18:53:14 [INFO] Validated directory: Temporary files
2025-05-20 18:53:14 [INFO] Validated directory: Candidate images
2025-05-20 18:53:14 [INFO] Validated directory: Fingerprint templates
2025-05-20 18:53:14 [INFO] All required files and directories validated successfully
2025-05-20 18:53:14 [INFO] Initializing application components
2025-05-20 18:53:14 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 18:53:14 [INFO] Hardware cache loaded with 10 entries
2025-05-20 18:53:14 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 18:53:14 [INFO] Room cache loaded with 3 entries
2025-05-20 18:53:14 [INFO] Loading seat assignments from seat ID sections
2025-05-20 18:53:14 [INFO] Loaded 1 seat assignments
2025-05-20 18:53:14 [INFO] Database manager initialized successfully
2025-05-20 18:53:14 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 18:53:14 [INFO] Webcam manager initialized
2025-05-20 18:53:14 [INFO] Webcam: Starting webcam...
2025-05-20 18:53:14 [INFO] Webcam: No webcam found at index 0
2025-05-20 18:53:14 [WARNING] Failed to start webcam
2025-05-20 18:53:14 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 18:53:14 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 18:53:14 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 18:53:14 [INFO] Added webcam feed control with default photo
2025-05-20 18:53:14 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 18:53:52 [INFO] Found seat assignment in cache for 9351: F1-R1-S8
2025-05-20 18:53:59 [INFO] No seat assignment found for 9353
2025-05-20 18:54:28 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 18:54:28 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 18:54:28 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 18:54:28 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 18:54:28 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 18:54:28 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 18:54:34 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 18:55:03 [INFO] Error handler initialized
2025-05-20 18:55:03 [INFO] Read database path from config: db
2025-05-20 18:55:03 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 18:55:03 [INFO] Error handler initialized
2025-05-20 18:55:03 [INFO] Starting WinCBT-Biometric (v1.4.6 Build 20250520)
2025-05-20 18:55:03 [INFO] PathManager initialized successfully
2025-05-20 18:55:03 [INFO] PathManager initialized successfully
2025-05-20 18:55:03 [INFO] Validating required files and directories
2025-05-20 18:55:03 [INFO] Validated directory: Database
2025-05-20 18:55:03 [INFO] Validated directory: Images
2025-05-20 18:55:03 [INFO] Validated directory: Logs
2025-05-20 18:55:03 [INFO] Validated directory: Temporary files
2025-05-20 18:55:03 [INFO] Validated directory: Candidate images
2025-05-20 18:55:03 [INFO] Validated directory: Fingerprint templates
2025-05-20 18:55:03 [INFO] All required files and directories validated successfully
2025-05-20 18:55:03 [INFO] Initializing application components
2025-05-20 18:55:03 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 18:55:03 [INFO] Hardware cache loaded with 10 entries
2025-05-20 18:55:03 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 18:55:03 [INFO] Room cache loaded with 3 entries
2025-05-20 18:55:03 [INFO] Loading seat assignments from seat ID sections
2025-05-20 18:55:03 [INFO] Loaded 1 seat assignments
2025-05-20 18:55:03 [INFO] Database manager initialized successfully
2025-05-20 18:55:03 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 18:55:03 [INFO] Webcam manager initialized
2025-05-20 18:55:03 [INFO] Webcam: Starting webcam...
2025-05-20 18:55:12 [INFO] Webcam: Webcam active (Index: 0)
2025-05-20 18:55:12 [INFO] Webcam started successfully
2025-05-20 18:55:12 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 18:55:12 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 18:55:12 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 18:55:12 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 18:55:13 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 18:55:13 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 18:55:13 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 18:55:13 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 18:55:13 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 18:55:13 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 18:55:13 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 18:55:13 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 18:55:13 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 18:55:13 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 18:55:13 [INFO] Added webcam feed control with default photo
2025-05-20 18:55:13 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 18:55:35 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 18:55:38 [INFO] Error handler initialized
2025-05-20 18:55:38 [INFO] Read database path from config: db
2025-05-20 18:55:38 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 18:55:38 [INFO] Error handler initialized
2025-05-20 18:55:38 [INFO] Starting WinCBT-Biometric (v1.4.6 Build 20250520)
2025-05-20 18:55:38 [INFO] PathManager initialized successfully
2025-05-20 18:55:38 [INFO] PathManager initialized successfully
2025-05-20 18:55:38 [INFO] Validating required files and directories
2025-05-20 18:55:38 [INFO] Validated directory: Database
2025-05-20 18:55:38 [INFO] Validated directory: Images
2025-05-20 18:55:38 [INFO] Validated directory: Logs
2025-05-20 18:55:38 [INFO] Validated directory: Temporary files
2025-05-20 18:55:38 [INFO] Validated directory: Candidate images
2025-05-20 18:55:38 [INFO] Validated directory: Fingerprint templates
2025-05-20 18:55:38 [INFO] All required files and directories validated successfully
2025-05-20 18:55:38 [INFO] Initializing application components
2025-05-20 18:55:38 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 18:55:38 [INFO] Hardware cache loaded with 10 entries
2025-05-20 18:55:38 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 18:55:38 [INFO] Room cache loaded with 3 entries
2025-05-20 18:55:38 [INFO] Loading seat assignments from seat ID sections
2025-05-20 18:55:38 [INFO] Loaded 1 seat assignments
2025-05-20 18:55:38 [INFO] Database manager initialized successfully
2025-05-20 18:55:38 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 18:55:38 [INFO] Webcam manager initialized
2025-05-20 18:55:38 [INFO] Webcam: Starting webcam...
2025-05-20 18:55:38 [INFO] Webcam: No webcam found at index 0
2025-05-20 18:55:38 [WARNING] Failed to start webcam
2025-05-20 18:55:38 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 18:55:38 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 18:55:38 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 18:55:38 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 18:55:38 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 18:55:38 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 18:55:38 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 18:55:38 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 18:55:38 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 18:55:38 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 18:55:38 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 18:55:38 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 18:55:38 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 18:55:38 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 18:55:38 [INFO] Added webcam feed control with default photo
2025-05-20 18:55:38 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 18:55:44 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 18:55:46 [INFO] Error handler initialized
2025-05-20 18:55:46 [INFO] Read database path from config: db
2025-05-20 18:55:46 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 18:55:46 [INFO] Error handler initialized
2025-05-20 18:55:46 [INFO] Starting WinCBT-Biometric (v1.4.6 Build 20250520)
2025-05-20 18:55:46 [INFO] PathManager initialized successfully
2025-05-20 18:55:46 [INFO] PathManager initialized successfully
2025-05-20 18:55:46 [INFO] Validating required files and directories
2025-05-20 18:55:46 [INFO] Validated directory: Database
2025-05-20 18:55:46 [INFO] Validated directory: Images
2025-05-20 18:55:46 [INFO] Validated directory: Logs
2025-05-20 18:55:46 [INFO] Validated directory: Temporary files
2025-05-20 18:55:46 [INFO] Validated directory: Candidate images
2025-05-20 18:55:46 [INFO] Validated directory: Fingerprint templates
2025-05-20 18:55:46 [INFO] All required files and directories validated successfully
2025-05-20 18:55:46 [INFO] Initializing application components
2025-05-20 18:55:46 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 18:55:46 [INFO] Hardware cache loaded with 10 entries
2025-05-20 18:55:46 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 18:55:46 [INFO] Room cache loaded with 3 entries
2025-05-20 18:55:46 [INFO] Loading seat assignments from seat ID sections
2025-05-20 18:55:46 [INFO] Loaded 1 seat assignments
2025-05-20 18:55:46 [INFO] Database manager initialized successfully
2025-05-20 18:55:46 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 18:55:46 [INFO] Webcam manager initialized
2025-05-20 18:55:46 [INFO] Webcam: Starting webcam...
2025-05-20 18:55:46 [INFO] Webcam: No webcam found at index 0
2025-05-20 18:55:46 [WARNING] Failed to start webcam
2025-05-20 18:55:46 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 18:55:46 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 18:55:46 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 18:55:46 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 18:55:46 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 18:55:46 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 18:55:46 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 18:55:46 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 18:55:46 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 18:55:46 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 18:55:46 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 18:55:46 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 18:55:46 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 18:55:46 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 18:55:46 [INFO] Added webcam feed control with default photo
2025-05-20 18:55:46 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 18:56:00 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 18:56:31 [INFO] Error handler initialized
2025-05-20 18:56:31 [INFO] Read database path from config: db
2025-05-20 18:56:31 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 18:56:31 [INFO] Error handler initialized
2025-05-20 18:56:31 [INFO] Starting WinCBT-Biometric (v1.4.6 Build 20250520)
2025-05-20 18:56:31 [INFO] PathManager initialized successfully
2025-05-20 18:56:31 [INFO] PathManager initialized successfully
2025-05-20 18:56:31 [INFO] Validating required files and directories
2025-05-20 18:56:31 [INFO] Validated directory: Database
2025-05-20 18:56:31 [INFO] Validated directory: Images
2025-05-20 18:56:31 [INFO] Webcam manager initialized
2025-05-20 18:56:31 [INFO] Webcam: Starting webcam...
2025-05-20 18:56:32 [INFO] Webcam: No webcam found at index 0
2025-05-20 18:56:32 [WARNING] Failed to start webcam
2025-05-20 18:56:32 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 18:56:32 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 18:56:32 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 18:56:32 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 18:56:32 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 18:56:32 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 18:56:32 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 18:56:32 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 18:56:32 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 18:56:32 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 18:56:32 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 18:56:32 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 18:56:32 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 18:56:32 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 18:56:32 [INFO] Added webcam feed control with default photo
2025-05-20 18:56:32 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 18:57:05 [INFO] No seat assignment found for 9353
2025-05-20 18:57:30 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 18:57:30 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 18:57:30 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 18:57:30 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 18:57:30 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 18:57:30 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 18:57:31 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 18:57:33 [INFO] Error handler initialized
2025-05-20 18:57:33 [INFO] Read database path from config: db
2025-05-20 18:57:33 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 18:57:33 [INFO] Error handler initialized
2025-05-20 18:57:33 [INFO] Starting WinCBT-Biometric (v1.4.6 Build 20250520)
2025-05-20 18:57:33 [INFO] PathManager initialized successfully
2025-05-20 18:57:33 [INFO] PathManager initialized successfully
2025-05-20 18:57:33 [INFO] Validating required files and directories
2025-05-20 18:57:33 [INFO] Validated directory: Database
2025-05-20 18:57:33 [INFO] Validated directory: Images
2025-05-20 18:57:33 [INFO] Validated directory: Logs
2025-05-20 18:57:33 [INFO] Validated directory: Temporary files
2025-05-20 18:57:33 [INFO] Validated directory: Candidate images
2025-05-20 18:57:33 [INFO] Validated directory: Fingerprint templates
2025-05-20 18:57:33 [INFO] All required files and directories validated successfully
2025-05-20 18:57:33 [INFO] Initializing application components
2025-05-20 18:57:33 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 18:57:33 [INFO] Hardware cache loaded with 10 entries
2025-05-20 18:57:33 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 18:57:33 [INFO] Room cache loaded with 3 entries
2025-05-20 18:57:33 [INFO] Loading seat assignments from seat ID sections
2025-05-20 18:57:33 [INFO] Loaded 1 seat assignments
2025-05-20 18:57:33 [INFO] Database manager initialized successfully
2025-05-20 18:57:33 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 18:57:33 [INFO] Webcam manager initialized
2025-05-20 18:57:33 [INFO] Webcam: Starting webcam...
2025-05-20 18:57:33 [INFO] Webcam: No webcam found at index 0
2025-05-20 18:57:33 [WARNING] Failed to start webcam
2025-05-20 18:57:33 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 18:57:33 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 18:57:33 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 18:57:33 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 18:57:33 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 18:57:34 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 18:57:34 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 18:57:34 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 18:57:34 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 18:57:34 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 18:57:34 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 18:57:34 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 18:57:34 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 18:57:34 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 18:57:34 [INFO] Added webcam feed control with default photo
2025-05-20 18:57:34 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 18:57:45 [INFO] Application exiting: Exit (Code: 0)
2025-05-20 20:53:28 [INFO] Error handler initialized
2025-05-20 20:53:28 [INFO] Read database path from config: db
2025-05-20 20:53:28 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-20 20:53:28 [INFO] Error handler initialized
2025-05-20 20:53:28 [INFO] Starting WinCBT-Biometric (v1.4.6 Build 20250520)
2025-05-20 20:53:28 [INFO] PathManager initialized successfully
2025-05-20 20:53:28 [INFO] PathManager initialized successfully
2025-05-20 20:53:28 [INFO] Validating required files and directories
2025-05-20 20:53:28 [INFO] Validated directory: Database
2025-05-20 20:53:28 [INFO] Validated directory: Images
2025-05-20 20:53:28 [INFO] Validated directory: Logs
2025-05-20 20:53:28 [INFO] Validated directory: Temporary files
2025-05-20 20:53:28 [INFO] Validated directory: Candidate images
2025-05-20 20:53:28 [INFO] Validated directory: Fingerprint templates
2025-05-20 20:53:28 [INFO] All required files and directories validated successfully
2025-05-20 20:53:28 [INFO] Initializing application components
2025-05-20 20:53:28 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-20 20:53:28 [INFO] Hardware cache loaded with 10 entries
2025-05-20 20:53:28 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-20 20:53:28 [INFO] Room cache loaded with 3 entries
2025-05-20 20:53:28 [INFO] Loading seat assignments from seat ID sections
2025-05-20 20:53:28 [INFO] Loaded 1 seat assignments
2025-05-20 20:53:28 [INFO] Database manager initialized successfully
2025-05-20 20:53:28 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-20 20:53:28 [INFO] Webcam manager initialized
2025-05-20 20:53:28 [INFO] Webcam: Starting webcam...
2025-05-20 20:53:28 [INFO] Webcam: No webcam found at index 0
2025-05-20 20:53:28 [WARNING] Failed to start webcam
2025-05-20 20:53:29 [INFO] Config: Verification.SignatureVerification = 0
2025-05-20 20:53:29 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-20 20:53:29 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-20 20:53:29 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-20 20:53:29 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-20 20:53:29 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-20 20:53:29 [INFO] Config: Verification.FingerprintMode = save
2025-05-20 20:53:29 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-20 20:53:29 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-20 20:53:29 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-20 20:53:29 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-20 20:53:29 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-20 20:53:29 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-20 20:53:29 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-20 20:53:29 [INFO] Added webcam feed control with default photo
2025-05-20 20:53:29 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-20 20:53:39 [INFO] Found seat assignment in cache for 9351: F1-R1-S8
2025-05-20 20:58:01 [INFO] Application exiting: Exit (Code: 0)

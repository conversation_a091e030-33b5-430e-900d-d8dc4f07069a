=== WinCBT-Biometric Error Log ===
Started: 2025-05-24 11:06:31
----------------------------------------
2025-05-24 11:06:31 [INFO] Error handler initialized
2025-05-24 11:06:32 [INFO] Read database path from config: db
2025-05-24 11:06:32 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:06:32 [INFO] Error handler initialized
2025-05-24 11:06:32 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:06:32 [INFO] PathManager initialized successfully
2025-05-24 11:06:32 [INFO] PathManager initialized successfully
2025-05-24 11:06:32 [INFO] Validating required files and directories
2025-05-24 11:06:32 [INFO] Validated directory: Database
2025-05-24 11:06:32 [INFO] Validated directory: Images
2025-05-24 11:06:32 [INFO] Validated directory: Logs
2025-05-24 11:06:32 [INFO] Validated directory: Temporary files
2025-05-24 11:06:32 [INFO] Validated directory: Candidate images
2025-05-24 11:06:32 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:06:32 [INFO] All required files and directories validated successfully
2025-05-24 11:06:32 [INFO] Initializing application components
2025-05-24 11:06:32 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:06:32 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:06:32 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:06:32 [INFO] Room cache loaded with 3 entries
2025-05-24 11:06:32 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:06:32 [INFO] Loaded 1 seat assignments
2025-05-24 11:06:32 [INFO] Database manager initialized successfully
2025-05-24 11:06:32 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:06:34 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:06:34 [DEBUG] IsObject check after creation: True
2025-05-24 11:06:34 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:06:34 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:06:34 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:06:34 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:06:34 [DEBUG] g_fingerprintManager class handle: 10397840
2025-05-24 11:06:35 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:06:36 [INFO] Webcam started successfully
2025-05-24 11:06:36 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:06:36 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:06:36 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:06:36 [INFO] Post-exam mode is enabled
2025-05-24 11:06:36 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:06:36 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:06:36 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:06:36 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:06:36 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:06:36 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:06:36 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:06:36 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:06:36 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:06:36 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:06:36 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:06:36 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:06:37 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:06:37 [INFO] Performing one-time device status check
2025-05-24 11:06:37 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:06:37 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:06:37 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:06:37 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:06:37 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:06:37 [INFO] One-time device status check complete
2025-05-24 11:06:44 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:06:45 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:06:45 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:06:45 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:06:45 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:06:46 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:06:46 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:06:46 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:06:46 [INFO] Global exam times - Start: 001200, End: 150000, Current: 110646
2025-05-24 11:06:46 [INFO] Global exam progress: 73.73498498498499% (Elapsed: 39286s, Duration: 53280s)
2025-05-24 11:06:46 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:06:46 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:06:59 [INFO] Loaded existing post-exam photo status: Verified
2025-05-24 11:07:03 [INFO] Post-exam verification status is Incomplete for candidate: 9350
2025-05-24 11:07:11 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:07:11 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:07:12 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:07:12 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:07:12 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:07:12 [INFO] No seat assignment found for 9356
2025-05-24 11:07:12 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9356
2025-05-24 11:07:12 [INFO] Entering post-exam verification mode for candidate: 9356
2025-05-24 11:07:21 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:07:21 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:07:21 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:07:21 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:07:21 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:07:22 [INFO] No seat assignment found for 9351
2025-05-24 11:07:22 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:07:22 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:07:28 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:07:28 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:07:28 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:07:28 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:07:28 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:07:35 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:07:35 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:07:36 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:07:36 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:07:36 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:07:36 [INFO] No seat assignment found for 9353
2025-05-24 11:07:36 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9353
2025-05-24 11:07:36 [INFO] Entering post-exam verification mode for candidate: 9353
2025-05-24 11:07:42 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:07:42 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:07:43 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:07:43 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:07:43 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:07:43 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:07:43 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:07:43 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:07:43 [INFO] Global exam times - Start: 001200, End: 150000, Current: 110743
2025-05-24 11:07:44 [INFO] Global exam progress: 73.841966966966964% (Elapsed: 39343s, Duration: 53280s)
2025-05-24 11:07:44 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:07:44 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:07:50 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:07:50 [INFO] Unloaded avicap32.dll library
2025-05-24 11:07:56 [INFO] Error handler initialized
2025-05-24 11:07:56 [INFO] Read database path from config: db
2025-05-24 11:07:56 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:07:56 [INFO] Error handler initialized
2025-05-24 11:07:56 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:07:56 [INFO] PathManager initialized successfully
2025-05-24 11:07:56 [INFO] PathManager initialized successfully
2025-05-24 11:07:56 [INFO] Validating required files and directories
2025-05-24 11:07:56 [INFO] Validated directory: Database
2025-05-24 11:07:56 [INFO] Validated directory: Images
2025-05-24 11:07:56 [INFO] Validated directory: Logs
2025-05-24 11:07:56 [INFO] Validated directory: Temporary files
2025-05-24 11:07:56 [INFO] Validated directory: Candidate images
2025-05-24 11:07:56 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:07:56 [INFO] All required files and directories validated successfully
2025-05-24 11:07:56 [INFO] Initializing application components
2025-05-24 11:07:56 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:07:56 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:07:56 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:07:56 [INFO] Room cache loaded with 3 entries
2025-05-24 11:07:56 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:07:56 [INFO] Loaded 1 seat assignments
2025-05-24 11:07:56 [INFO] Database manager initialized successfully
2025-05-24 11:07:56 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:07:57 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:07:57 [DEBUG] IsObject check after creation: True
2025-05-24 11:07:57 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:07:58 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:07:58 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:07:58 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:07:58 [DEBUG] g_fingerprintManager class handle: 10342560
2025-05-24 11:07:58 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:07:59 [INFO] Webcam started successfully
2025-05-24 11:07:59 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:07:59 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:07:59 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:07:59 [INFO] Post-exam mode is enabled
2025-05-24 11:07:59 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:07:59 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:07:59 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:07:59 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:07:59 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:07:59 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:07:59 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:07:59 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:07:59 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:07:59 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:07:59 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:07:59 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:07:59 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:07:59 [INFO] Performing one-time device status check
2025-05-24 11:07:59 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:07:59 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:07:59 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:07:59 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:07:59 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:07:59 [INFO] One-time device status check complete
2025-05-24 11:08:03 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:08:03 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:08:03 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:08:03 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:08:03 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:08:04 [INFO] No seat assignment found for 9351
2025-05-24 11:08:04 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:08:04 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:08:10 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:08:10 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:08:10 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:08:10 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:08:11 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:08:11 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:08:11 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:08:11 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:08:11 [INFO] Global exam times - Start: 001200, End: 150000, Current: 110811
2025-05-24 11:08:12 [INFO] Global exam progress: 73.89451951951952% (Elapsed: 39371s, Duration: 53280s)
2025-05-24 11:08:12 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:08:12 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:08:47 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:08:48 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:08:48 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:08:48 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:08:48 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:08:49 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:08:49 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:08:49 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:08:49 [INFO] Global exam times - Start: 001200, End: 150000, Current: 110849
2025-05-24 11:08:49 [INFO] Global exam progress: 73.965840840840841% (Elapsed: 39409s, Duration: 53280s)
2025-05-24 11:08:49 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:08:49 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:09:02 [INFO] Post-Exam Mode disabled by user
2025-05-24 11:09:06 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:09:06 [INFO] Unloaded avicap32.dll library
2025-05-24 11:09:11 [INFO] Error handler initialized
2025-05-24 11:09:11 [INFO] Read database path from config: db
2025-05-24 11:09:11 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:09:11 [INFO] Error handler initialized
2025-05-24 11:09:11 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:09:11 [INFO] PathManager initialized successfully
2025-05-24 11:09:11 [INFO] PathManager initialized successfully
2025-05-24 11:09:11 [INFO] Validating required files and directories
2025-05-24 11:09:11 [INFO] Validated directory: Database
2025-05-24 11:09:11 [INFO] Validated directory: Images
2025-05-24 11:09:11 [INFO] Validated directory: Logs
2025-05-24 11:09:11 [INFO] Validated directory: Temporary files
2025-05-24 11:09:11 [INFO] Validated directory: Candidate images
2025-05-24 11:09:11 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:09:11 [INFO] All required files and directories validated successfully
2025-05-24 11:09:11 [INFO] Initializing application components
2025-05-24 11:09:11 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:09:11 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:09:11 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:09:11 [INFO] Room cache loaded with 3 entries
2025-05-24 11:09:11 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:09:11 [INFO] Loaded 1 seat assignments
2025-05-24 11:09:11 [INFO] Database manager initialized successfully
2025-05-24 11:09:11 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:09:12 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:09:12 [DEBUG] IsObject check after creation: True
2025-05-24 11:09:12 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:09:13 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:09:13 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:09:13 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:09:13 [DEBUG] g_fingerprintManager class handle: 1341264
2025-05-24 11:09:13 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:09:14 [INFO] Webcam started successfully
2025-05-24 11:09:14 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:09:14 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:09:14 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-24 11:09:14 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:09:14 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:09:14 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:09:14 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:09:14 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:09:14 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:09:14 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:09:14 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:09:14 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:09:14 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:09:14 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:09:14 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:09:14 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:09:14 [INFO] Performing one-time device status check
2025-05-24 11:09:14 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:09:14 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:09:14 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:09:14 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:09:14 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:09:15 [INFO] One-time device status check complete
2025-05-24 11:09:19 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:09:20 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:09:20 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:09:20 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:09:20 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:09:21 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:09:30 [INFO] Post-Exam Mode enabled by user
2025-05-24 11:09:34 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:09:35 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:09:35 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:09:35 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:09:35 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:09:36 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:09:36 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:09:36 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:09:36 [INFO] Global exam times - Start: 001200, End: 150000, Current: 110936
2025-05-24 11:09:36 [INFO] Global exam progress: 74.054054054054049% (Elapsed: 39456s, Duration: 53280s)
2025-05-24 11:09:36 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:09:36 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:09:56 [INFO] Loaded existing post-exam photo status: Verified
2025-05-24 11:10:00 [INFO] Post-exam verification status is Incomplete for candidate: 9350
2025-05-24 11:10:05 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:10:06 [INFO] Unloaded avicap32.dll library
2025-05-24 11:10:09 [INFO] Error handler initialized
2025-05-24 11:10:09 [INFO] Read database path from config: db
2025-05-24 11:10:09 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:10:09 [INFO] Error handler initialized
2025-05-24 11:10:09 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:10:09 [INFO] PathManager initialized successfully
2025-05-24 11:10:09 [INFO] PathManager initialized successfully
2025-05-24 11:10:09 [INFO] Validating required files and directories
2025-05-24 11:10:09 [INFO] Validated directory: Database
2025-05-24 11:10:09 [INFO] Validated directory: Images
2025-05-24 11:10:09 [INFO] Validated directory: Logs
2025-05-24 11:10:09 [INFO] Validated directory: Temporary files
2025-05-24 11:10:09 [INFO] Validated directory: Candidate images
2025-05-24 11:10:09 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:10:09 [INFO] All required files and directories validated successfully
2025-05-24 11:10:09 [INFO] Initializing application components
2025-05-24 11:10:09 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:10:09 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:10:09 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:10:09 [INFO] Room cache loaded with 3 entries
2025-05-24 11:10:09 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:10:09 [INFO] Loaded 1 seat assignments
2025-05-24 11:10:09 [INFO] Database manager initialized successfully
2025-05-24 11:10:09 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:10:10 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:10:10 [DEBUG] IsObject check after creation: True
2025-05-24 11:10:10 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:10:11 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:10:11 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:10:11 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:10:11 [DEBUG] g_fingerprintManager class handle: 50991712
2025-05-24 11:10:11 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:10:12 [INFO] Webcam started successfully
2025-05-24 11:10:12 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:10:12 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:10:12 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:10:12 [INFO] Post-exam mode is enabled
2025-05-24 11:10:12 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:10:12 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:10:12 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:10:12 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:10:12 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:10:12 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:10:12 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:10:12 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:10:12 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:10:12 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:10:12 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:10:12 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:10:12 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:10:12 [INFO] Performing one-time device status check
2025-05-24 11:10:12 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:10:12 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:10:13 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:10:13 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:10:13 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:10:13 [INFO] One-time device status check complete
2025-05-24 11:10:17 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:10:17 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:10:18 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:10:18 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:10:18 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:10:18 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:10:18 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:10:18 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:10:18 [INFO] Global exam times - Start: 001200, End: 150000, Current: 111018
2025-05-24 11:10:18 [INFO] Global exam progress: 74.132882882882882% (Elapsed: 39498s, Duration: 53280s)
2025-05-24 11:10:18 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:10:18 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:11:41 [INFO] Loaded existing post-exam photo status: Verified
2025-05-24 11:12:02 [INFO] Post-exam verification status is Incomplete for candidate: 9350
2025-05-24 11:12:51 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:12:52 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:12:52 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:12:52 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:12:52 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:12:53 [INFO] No seat assignment found for 9351
2025-05-24 11:12:53 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:12:53 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:13:00 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:13:00 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:13:01 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:13:01 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:13:01 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:13:01 [INFO] No seat assignment found for 9359
2025-05-24 11:13:01 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9359
2025-05-24 11:13:01 [INFO] Entering post-exam verification mode for candidate: 9359
2025-05-24 11:13:05 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:13:06 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:13:06 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:13:06 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:13:06 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:13:06 [INFO] No seat assignment found for 9351
2025-05-24 11:13:06 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:13:06 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:13:33 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:13:34 [INFO] Unloaded avicap32.dll library
2025-05-24 11:13:38 [INFO] Error handler initialized
2025-05-24 11:13:38 [INFO] Read database path from config: db
2025-05-24 11:13:38 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:13:38 [INFO] Error handler initialized
2025-05-24 11:13:38 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:13:38 [INFO] PathManager initialized successfully
2025-05-24 11:13:38 [INFO] PathManager initialized successfully
2025-05-24 11:13:38 [INFO] Validating required files and directories
2025-05-24 11:13:38 [INFO] Validated directory: Database
2025-05-24 11:13:38 [INFO] Validated directory: Images
2025-05-24 11:13:38 [INFO] Validated directory: Logs
2025-05-24 11:13:38 [INFO] Validated directory: Temporary files
2025-05-24 11:13:38 [INFO] Validated directory: Candidate images
2025-05-24 11:13:38 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:13:38 [INFO] All required files and directories validated successfully
2025-05-24 11:13:38 [INFO] Initializing application components
2025-05-24 11:13:38 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:13:38 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:13:38 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:13:38 [INFO] Room cache loaded with 3 entries
2025-05-24 11:13:38 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:13:38 [INFO] Loaded 1 seat assignments
2025-05-24 11:13:38 [INFO] Database manager initialized successfully
2025-05-24 11:13:38 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:13:40 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:13:40 [DEBUG] IsObject check after creation: True
2025-05-24 11:13:40 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:13:40 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:13:40 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:13:40 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:13:40 [DEBUG] g_fingerprintManager class handle: 10066080
2025-05-24 11:13:40 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:13:41 [INFO] Webcam started successfully
2025-05-24 11:13:41 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:13:41 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:13:41 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:13:41 [INFO] Post-exam mode is enabled
2025-05-24 11:13:41 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:13:41 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:13:41 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:13:41 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:13:41 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:13:41 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:13:41 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:13:41 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:13:41 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:13:41 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:13:41 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:13:41 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:13:41 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:13:42 [INFO] Performing one-time device status check
2025-05-24 11:13:42 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:13:42 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:13:42 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:13:42 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:13:42 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:13:42 [INFO] One-time device status check complete
2025-05-24 11:13:52 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:13:53 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:13:53 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:13:53 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:13:53 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:13:54 [INFO] No seat assignment found for 9351
2025-05-24 11:13:54 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:13:54 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:14:00 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:14:01 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:14:01 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:14:01 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:14:01 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:14:02 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:14:02 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:14:02 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:14:02 [INFO] Global exam times - Start: 001200, End: 150000, Current: 111402
2025-05-24 11:14:02 [INFO] Global exam progress: 74.553303303303309% (Elapsed: 39722s, Duration: 53280s)
2025-05-24 11:14:02 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:14:02 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:14:07 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 11:14:07 [INFO] Unloaded avicap32.dll library
2025-05-24 11:14:10 [INFO] Error handler initialized
2025-05-24 11:14:10 [INFO] Read database path from config: db
2025-05-24 11:14:10 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:14:10 [INFO] Error handler initialized
2025-05-24 11:14:10 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:14:10 [INFO] PathManager initialized successfully
2025-05-24 11:14:10 [INFO] PathManager initialized successfully
2025-05-24 11:14:10 [INFO] Validating required files and directories
2025-05-24 11:14:10 [INFO] Validated directory: Database
2025-05-24 11:14:10 [INFO] Validated directory: Images
2025-05-24 11:14:10 [INFO] Validated directory: Logs
2025-05-24 11:14:10 [INFO] Validated directory: Temporary files
2025-05-24 11:14:10 [INFO] Validated directory: Candidate images
2025-05-24 11:14:10 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:14:10 [INFO] All required files and directories validated successfully
2025-05-24 11:14:10 [INFO] Initializing application components
2025-05-24 11:14:10 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:14:10 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:14:10 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:14:10 [INFO] Room cache loaded with 3 entries
2025-05-24 11:14:10 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:14:10 [INFO] Loaded 1 seat assignments
2025-05-24 11:14:10 [INFO] Database manager initialized successfully
2025-05-24 11:14:10 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:14:12 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:14:12 [DEBUG] IsObject check after creation: True
2025-05-24 11:14:12 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:14:12 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:14:12 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:14:13 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:14:13 [DEBUG] g_fingerprintManager class handle: 50331152
2025-05-24 11:14:13 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:14:13 [INFO] Webcam started successfully
2025-05-24 11:14:13 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:14:13 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:14:13 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:14:13 [INFO] Post-exam mode is enabled
2025-05-24 11:14:13 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:14:13 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:14:13 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:14:13 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:14:13 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:14:13 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:14:13 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:14:13 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:14:14 [INFO] Performing one-time device status check
2025-05-24 11:14:14 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:14:14 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:14:14 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:14:14 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:14:14 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:14:14 [INFO] One-time device status check complete
2025-05-24 11:14:19 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:14:20 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:14:20 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:14:20 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:14:20 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:14:21 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:14:21 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:14:21 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:14:21 [INFO] Global exam times - Start: 001200, End: 150000, Current: 111421
2025-05-24 11:14:21 [INFO] Global exam progress: 74.588963963963963% (Elapsed: 39741s, Duration: 53280s)
2025-05-24 11:14:21 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:14:21 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:14:24 [INFO] Application exiting: Reload (Code: 0)
2025-05-24 11:14:24 [INFO] Unloaded avicap32.dll library
2025-05-24 11:14:24 [INFO] Error handler initialized
2025-05-24 11:14:24 [INFO] Read database path from config: db
2025-05-24 11:14:24 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:14:24 [INFO] Error handler initialized
2025-05-24 11:14:24 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:14:24 [INFO] PathManager initialized successfully
2025-05-24 11:14:24 [INFO] PathManager initialized successfully
2025-05-24 11:14:24 [INFO] Validating required files and directories
2025-05-24 11:14:24 [INFO] Validated directory: Database
2025-05-24 11:14:24 [INFO] Validated directory: Images
2025-05-24 11:14:24 [INFO] Validated directory: Logs
2025-05-24 11:14:24 [INFO] Validated directory: Temporary files
2025-05-24 11:14:24 [INFO] Validated directory: Candidate images
2025-05-24 11:14:24 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:14:24 [INFO] All required files and directories validated successfully
2025-05-24 11:14:24 [INFO] Initializing application components
2025-05-24 11:14:24 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:14:24 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:14:24 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:14:24 [INFO] Room cache loaded with 3 entries
2025-05-24 11:14:24 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:14:24 [INFO] Loaded 1 seat assignments
2025-05-24 11:14:24 [INFO] Database manager initialized successfully
2025-05-24 11:14:24 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:14:26 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:14:26 [DEBUG] IsObject check after creation: True
2025-05-24 11:14:26 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:14:27 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:14:27 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:14:27 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:14:27 [DEBUG] g_fingerprintManager class handle: 49809824
2025-05-24 11:14:27 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:14:27 [INFO] Webcam started successfully
2025-05-24 11:14:27 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:14:27 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:14:27 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:14:27 [INFO] Post-exam mode is enabled
2025-05-24 11:14:27 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:14:27 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:14:27 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:14:27 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:14:27 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:14:27 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:14:27 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:14:27 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:14:27 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:14:27 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:14:28 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:14:28 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:14:28 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:14:28 [INFO] Performing one-time device status check
2025-05-24 11:14:28 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:14:28 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:14:28 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:14:28 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:14:28 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:14:28 [INFO] One-time device status check complete
2025-05-24 11:14:31 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:14:32 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:14:32 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:14:32 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:14:32 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:14:33 [INFO] No seat assignment found for 9351
2025-05-24 11:14:33 [WARNING] Post-exam verification attempted for candidate without seat assignment: 9351
2025-05-24 11:14:33 [INFO] Entering post-exam verification mode for candidate: 9351
2025-05-24 11:14:38 [INFO] Camera stopped and status updated: Not Connected
2025-05-24 11:14:39 [INFO] Camera started and status updated: HD Pro Webcam C920
2025-05-24 11:14:39 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:14:39 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:14:39 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:14:40 [INFO] Found seat assignment in cache for 9350: F1-R1-S8
2025-05-24 11:14:40 [ERROR] Error in IsPostExamVerification: This value of type "String" has no method named "GetCandidateSeat".
2025-05-24 11:14:40 [INFO] IsPostExamVerification: Error occurred, trying global timing as fallback
2025-05-24 11:14:40 [INFO] Global exam times - Start: 001200, End: 150000, Current: 111440
2025-05-24 11:14:40 [INFO] Global exam progress: 74.62462462462463% (Elapsed: 39760s, Duration: 53280s)
2025-05-24 11:14:40 [INFO] Is post-exam mode based on global timing: Yes
2025-05-24 11:14:40 [INFO] Entering post-exam verification mode for candidate: 9350
2025-05-24 11:17:44 [INFO] Application exiting: Single (Code: 0)
2025-05-24 11:17:44 [INFO] Unloaded avicap32.dll library
2025-05-24 11:17:44 [INFO] Error handler initialized
2025-05-24 11:17:44 [INFO] Read database path from config: db
2025-05-24 11:17:44 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-24 11:17:44 [INFO] Error handler initialized
2025-05-24 11:17:44 [INFO] Starting WinCBT-Biometric (v1.5.0 Build 20250523)
2025-05-24 11:17:44 [INFO] PathManager initialized successfully
2025-05-24 11:17:45 [INFO] PathManager initialized successfully
2025-05-24 11:17:45 [INFO] Validating required files and directories
2025-05-24 11:17:45 [INFO] Validated directory: Database
2025-05-24 11:17:45 [INFO] Validated directory: Images
2025-05-24 11:17:45 [INFO] Validated directory: Logs
2025-05-24 11:17:45 [INFO] Validated directory: Temporary files
2025-05-24 11:17:45 [INFO] Validated directory: Candidate images
2025-05-24 11:17:45 [INFO] Validated directory: Fingerprint templates
2025-05-24 11:17:45 [INFO] All required files and directories validated successfully
2025-05-24 11:17:45 [INFO] Initializing application components
2025-05-24 11:17:45 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-24 11:17:45 [INFO] Hardware cache loaded with 10 entries
2025-05-24 11:17:45 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-24 11:17:45 [INFO] Room cache loaded with 3 entries
2025-05-24 11:17:45 [INFO] Loading seat assignments from seat ID sections
2025-05-24 11:17:45 [INFO] Loaded 1 seat assignments
2025-05-24 11:17:45 [INFO] Database manager initialized successfully
2025-05-24 11:17:45 [DEBUG] Attempting to initialize SecuGenFingerprint
2025-05-24 11:17:46 [DEBUG] g_fingerprintManager type after creation: SecuGenFingerprint
2025-05-24 11:17:46 [DEBUG] IsObject check after creation: True
2025-05-24 11:17:46 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-24 11:17:47 [INFO] Fingerprint reader LED blinked twice during startup
2025-05-24 11:17:47 [INFO] Fingerprint reader status set to: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:17:47 [DEBUG] Final check - g_fingerprintManager is valid
2025-05-24 11:17:47 [DEBUG] g_fingerprintManager class handle: 10277488
2025-05-24 11:17:47 [INFO] Read camera name from config: HD Pro Webcam C920
2025-05-24 11:17:48 [INFO] Webcam started successfully
2025-05-24 11:17:48 [INFO] Config: Verification.SignatureVerification = 0
2025-05-24 11:17:48 [INFO] Config: Verification.RightThumbprintVerification = 1
2025-05-24 11:17:48 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-24 11:17:48 [INFO] Post-exam mode is enabled
2025-05-24 11:17:48 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-24 11:17:48 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-24 11:17:48 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-24 11:17:48 [INFO] Config: Verification.FingerprintMode = save
2025-05-24 11:17:48 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-24 11:17:48 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-24 11:17:48 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-24 11:17:48 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-24 11:17:48 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-24 11:17:48 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-24 11:17:48 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-24 11:17:48 [INFO] Using webcam controls initialized in constructor
2025-05-24 11:17:48 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-24 11:17:48 [INFO] Performing one-time device status check
2025-05-24 11:17:48 [INFO] Camera status: Connected (internal: HD Pro Webcam C920)
2025-05-24 11:17:48 [INFO] Fingerprint reader status: Connected (H58230901549) (internal: Auto-detected Device)
2025-05-24 11:17:49 [DEBUG] g_fingerprintDeviceSN value: 'H58230901549', type: String
2025-05-24 11:17:49 [DEBUG] Using serial number in footer: H58230901549
2025-05-24 11:17:49 [INFO] Device status updated in footer: Camera=HD Pro Webcam C920, Fingerprint=Connected (H58230901549)
2025-05-24 11:17:49 [INFO] One-time device status check complete

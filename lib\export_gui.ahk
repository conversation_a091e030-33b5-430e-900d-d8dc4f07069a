#Requires AutoHotkey v2.0

; Export GUI for WinCBT-Biometric
; This module provides the user interface for exporting candidate data

class ExportGUI {
    ; GUI objects
    gui := ""
    exportManager := ""

    ; GUI controls
    filePathEdit := ""
    delimiterDropdown := ""
    includeHeadersCheckbox := ""
    exportButton := ""
    cancelButton := ""
    progressBar := ""
    statusText := ""

    ; Filter controls
    filterGroupBox := ""
    filterByStatusCheckbox := ""
    statusDropdown := ""
    filterByDateCheckbox := ""
    startDatePicker := ""
    endDatePicker := ""
    filterBySpecialCheckbox := ""
    specialDropdown := ""

    ; Constructor
    __New() {
        ; Using static class, no need to create an instance
        global CSVMinimal
        this.exportManager := CSVMinimal
    }

    ; Show the export GUI
    Show() {
        ; Create GUI
        this.gui := Gui("+Resize", "Export Candidates")
        this.gui.OnEvent("Close", (*) => this.gui.Destroy())
        this.gui.OnEvent("Size", this.OnResize.Bind(this))

        ; Add controls
        this.gui.SetFont("s10")
        this.gui.Add("Text", "x20 y20 w100", "Export File:")
        this.filePathEdit := this.gui.Add("Edit", "x130 y20 w300 r1")
        browseButton := this.gui.Add("Button", "x440 y20 w80 h24 +0x1000", "Browse...")
        browseButton.OnEvent("Click", this.OnBrowse.Bind(this))

        this.gui.Add("Text", "x20 y60 w100", "Delimiter:")
        this.delimiterDropdown := this.gui.Add("DropDownList", "x130 y60 w100", ["Comma (,)", "Semicolon (;)", "Tab", "Pipe (|)"])
        this.delimiterDropdown.Choose(1)

        this.includeHeadersCheckbox := this.gui.Add("CheckBox", "x20 y100 w400 Checked", "Include headers")

        ; Add filter controls
        this.filterGroupBox := this.gui.Add("GroupBox", "x20 y140 w500 h180", "Filter Options")

        this.filterByStatusCheckbox := this.gui.Add("CheckBox", "x40 y170 w150", "Filter by Status:")
        this.statusDropdown := this.gui.Add("DropDownList", "x200 y170 w150 Disabled", ["Active", "Inactive", "Verified", "Pending"])
        this.statusDropdown.Choose(1)
        this.filterByStatusCheckbox.OnEvent("Click", (*) => this.statusDropdown.Enabled := this.filterByStatusCheckbox.Value)

        this.filterByDateCheckbox := this.gui.Add("CheckBox", "x40 y210 w150", "Filter by Date:")
        this.gui.Add("Text", "x200 y210 w40", "From:")
        this.startDatePicker := this.gui.Add("DateTime", "x240 y210 w110 Disabled", "yyyy-MM-dd")
        this.gui.Add("Text", "x360 y210 w30", "To:")
        this.endDatePicker := this.gui.Add("DateTime", "x390 y210 w110 Disabled", "yyyy-MM-dd")
        this.filterByDateCheckbox.OnEvent("Click", this.OnDateFilterClick.Bind(this))

        this.filterBySpecialCheckbox := this.gui.Add("CheckBox", "x40 y250 w150", "Special Candidates:")
        this.specialDropdown := this.gui.Add("DropDownList", "x200 y250 w150 Disabled", ["Only Special", "Exclude Special", "All Candidates"])
        this.specialDropdown.Choose(3)
        this.filterBySpecialCheckbox.OnEvent("Click", (*) => this.specialDropdown.Enabled := this.filterBySpecialCheckbox.Value)

        ; Add action buttons
        this.exportButton := this.gui.Add("Button", "x130 y340 w120 h30 +0x1000", "Export")
        this.exportButton.OnEvent("Click", this.OnExport.Bind(this))

        this.cancelButton := this.gui.Add("Button", "x260 y340 w120 h30 +0x1000", "Cancel")
        this.cancelButton.OnEvent("Click", (*) => this.gui.Destroy())

        this.gui.Add("Text", "x20 y390 w100", "Progress:")
        this.progressBar := this.gui.Add("Progress", "x130 y390 w390 h20")

        this.statusText := this.gui.Add("Text", "x20 y420 w500")

        ; Add log text area
        this.gui.Add("Text", "x20 y450 w100", "Export Log:")
        this.logEdit := this.gui.Add("Edit", "x20 y470 w500 h150 ReadOnly")

        ; Show GUI
        this.gui.Show("w540 h640")
    }

    ; Handle GUI resize
    OnResize(thisGui, minMax, width, height) {
        if (minMax = -1) ; If minimized
            return

        ; Resize controls
        this.filePathEdit.Move(, , width - 240)
        this.filterGroupBox.Move(, , width - 40)
        this.progressBar.Move(, , width - 150)
        this.logEdit.Move(, , width - 40, height - 490)
    }

    ; Handle browse button click
    OnBrowse(*) {
        selectedFile := FileSelect("S16", , "Save Export File", "CSV Files (*.csv)")
        if (selectedFile) {
            this.filePathEdit.Value := selectedFile
        }
    }

    ; Handle date filter checkbox click
    OnDateFilterClick(*) {
        this.startDatePicker.Enabled := this.filterByDateCheckbox.Value
        this.endDatePicker.Enabled := this.filterByDateCheckbox.Value
    }

    ; Handle export button click
    OnExport(*) {
        ; Get file path
        filePath := this.filePathEdit.Value
        if (!filePath) {
            MsgBox("Please specify an export file path.", "Export Error", "Icon!")
            return
        }

        ; Add .csv extension if not present
        if (!RegExMatch(filePath, "\.csv$")) {
            filePath .= ".csv"
            this.filePathEdit.Value := filePath
        }

        ; Get delimiter
        delimiterChoice := this.delimiterDropdown.Value
        delimiter := ","
        switch delimiterChoice {
            case "Semicolon (;)": delimiter := ";"
            case "Tab": delimiter := "`t"
            case "Pipe (|)": delimiter := "|"
        }

        ; Get include headers option
        includeHeaders := this.includeHeadersCheckbox.Value

        ; Disable controls during export
        this.exportButton.Enabled := false
        this.cancelButton.Enabled := false
        this.filePathEdit.Enabled := false
        this.delimiterDropdown.Enabled := false
        this.includeHeadersCheckbox.Enabled := false
        this.filterByStatusCheckbox.Enabled := false
        this.statusDropdown.Enabled := false
        this.filterByDateCheckbox.Enabled := false
        this.startDatePicker.Enabled := false
        this.endDatePicker.Enabled := false
        this.filterBySpecialCheckbox.Enabled := false
        this.specialDropdown.Enabled := false

        ; Clear log
        this.logEdit.Value := ""

        ; Set options
        options := {
            delimiter: delimiter,
            includeHeaders: includeHeaders
        }

        ; Create filter function based on selected filters
        filterFunc := this.CreateFilterFunction()
        if (filterFunc) {
            options.filter := filterFunc
        }

        ; Start export in a new thread
        SetTimer(() => this.PerformExport(filePath, options), -1)
    }

    ; Create a filter function based on selected filters
    CreateFilterFunction() {
        ; Initialize filter conditions
        conditions := []

        ; Status filter
        if (this.filterByStatusCheckbox.Value) {
            status := this.statusDropdown.Text
            statusFilter(candidate) {
                return candidate.Status = status
            }
            conditions.Push(statusFilter)
        }

        ; Date filter
        if (this.filterByDateCheckbox.Value) {
            startDate := this.startDatePicker.Value
            endDate := this.endDatePicker.Value
            ; TODO: Implement date filtering logic
        }

        ; Special candidates filter
        if (this.filterBySpecialCheckbox.Value) {
            specialOption := this.specialDropdown.Value
            if (specialOption = 1) { ; Only Special
                specialFilterInclude(candidate) {
                    return candidate.Special = "1"
                }
                conditions.Push(specialFilterInclude)
            } else if (specialOption = 2) { ; Exclude Special
                specialFilterExclude(candidate) {
                    return candidate.Special = "0"
                }
                conditions.Push(specialFilterExclude)
            }
            ; Option 3 (All Candidates) doesn't need a filter
        }

        ; If no conditions, return empty filter
        if (conditions.Length = 0) {
            return ""
        }

        ; Create combined filter function
        filterFunc(candidate) {
            for condition in conditions {
                if (!condition(candidate)) {
                    return false
                }
            }
            return true
        }
        return filterFunc
    }

    ; Perform the export operation
    PerformExport(filePath, options) {
        ; Update status
        this.statusText.Value := "Exporting candidates..."
        this.progressBar.Value := 0

        ; Log start
        this.AppendLog("Starting export to: " filePath)
        this.AppendLog("Options: Delimiter=" options.delimiter ", IncludeHeaders=" options.includeHeaders)

        ; Perform export
        try {
            results := this.exportManager.ExportCandidatesToCSV(filePath, options)

            ; Update progress
            this.progressBar.Value := 100

            ; Log results
            this.AppendLog("Export completed.")
            this.AppendLog("Total candidates: " results.totalCandidates)
            this.AppendLog("Candidates exported: " results.exported)

            ; Log errors
            if (results.errors.Length > 0) {
                this.AppendLog("Errors:")
                for error in results.errors {
                    this.AppendLog("  - " error)
                }
            }

            ; Update status
            this.statusText.Value := "Export completed: " results.exported " candidates exported"

            ; Show completion message
            MsgBox("Export completed.`n`nTotal candidates: " results.totalCandidates "`nExported: " results.exported, "Export Complete", "Icon!")
        } catch as err {
            ; Log error
            this.AppendLog("Error during export: " err.Message)

            ; Update status
            this.statusText.Value := "Export failed: " err.Message

            ; Show error message
            MsgBox("Export failed: " err.Message, "Export Error", "Icon!")
        }

        ; Re-enable controls
        this.exportButton.Enabled := true
        this.cancelButton.Enabled := true
        this.filePathEdit.Enabled := true
        this.delimiterDropdown.Enabled := true
        this.includeHeadersCheckbox.Enabled := true
        this.filterByStatusCheckbox.Enabled := true
        this.statusDropdown.Enabled := this.filterByStatusCheckbox.Value
        this.filterByDateCheckbox.Enabled := true
        this.startDatePicker.Enabled := this.filterByDateCheckbox.Value
        this.endDatePicker.Enabled := this.filterByDateCheckbox.Value
        this.filterBySpecialCheckbox.Enabled := true
        this.specialDropdown.Enabled := this.filterBySpecialCheckbox.Value
    }

    ; Append text to the log
    AppendLog(text) {
        currentLog := this.logEdit.Value
        if (currentLog != "") {
            currentLog .= "`r`n"
        }
        this.logEdit.Value := currentLog text

        ; Scroll to bottom
        this.logEdit.SendMsg(0x115, 7) ; WM_VSCROLL, SB_BOTTOM
    }
}

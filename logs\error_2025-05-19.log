=== WinCBT-Biometric Error Log ===
Started: 2025-05-19 20:04:39
----------------------------------------
2025-05-19 20:04:39 [INFO] Error handler initialized
2025-05-19 20:04:39 [INFO] Read database path from config: db
2025-05-19 20:04:39 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:04:39 [INFO] Error handler initialized
2025-05-19 20:04:39 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:04:39 [INFO] Set default Bin path in config.ini: bin
2025-05-19 20:04:39 [INFO] Set default Database path in config.ini: db
2025-05-19 20:04:39 [INFO] Set default Export path in config.ini: export
2025-05-19 20:04:39 [INFO] Set default Images path in config.ini: img
2025-05-19 20:04:39 [INFO] Set default Import path in config.ini: import
2025-05-19 20:04:39 [INFO] Set default Logs path in config.ini: logs
2025-05-19 20:04:39 [INFO] Created images Icons directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\icons\
2025-05-19 20:04:39 [INFO] Created images Captured directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\captured\
2025-05-19 20:04:39 [INFO] PathManager initialized successfully
2025-05-19 20:04:39 [INFO] PathManager initialized successfully
2025-05-19 20:04:39 [INFO] Validating required files and directories
2025-05-19 20:04:39 [INFO] Validated directory: Database
2025-05-19 20:04:39 [INFO] Validated directory: Images
2025-05-19 20:04:39 [INFO] Validated directory: Logs
2025-05-19 20:04:39 [INFO] Validated directory: Temporary files
2025-05-19 20:04:39 [INFO] Validated directory: Candidate images
2025-05-19 20:04:40 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:04:40 [WARNING] Company logo not found, creating placeholder
2025-05-19 20:04:40 [INFO] Created empty company logo placeholder
2025-05-19 20:04:40 [INFO] All required files and directories validated successfully
2025-05-19 20:04:40 [INFO] Initializing application components
2025-05-19 20:04:40 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:04:40 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:04:40 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:04:40 [INFO] Room cache loaded with 3 entries
2025-05-19 20:04:40 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:04:40 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:04:40 [INFO] Database manager initialized successfully
2025-05-19 20:04:40 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:04:40 [INFO] Webcam manager initialized
2025-05-19 20:04:40 [INFO] Webcam: Starting webcam...
2025-05-19 20:04:41 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:04:41 [INFO] Webcam started successfully
2025-05-19 20:04:41 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:04:41 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:04:41 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:04:41 [INFO] Post-exam mode is enabled
2025-05-19 20:04:41 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:04:41 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:04:41 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:04:41 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:04:41 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:04:41 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:04:41 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:04:41 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:04:41 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:04:41 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:04:41 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 20:04:41 [INFO] Added webcam feed control with default photo
2025-05-19 20:04:41 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 20:05:46 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 20:08:16 [INFO] Error handler initialized
2025-05-19 20:08:16 [INFO] Read database path from config: db
2025-05-19 20:08:16 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:08:16 [INFO] Error handler initialized
2025-05-19 20:08:16 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:08:16 [INFO] PathManager initialized successfully
2025-05-19 20:08:16 [INFO] PathManager initialized successfully
2025-05-19 20:08:16 [INFO] Validating required files and directories
2025-05-19 20:08:16 [INFO] Validated directory: Database
2025-05-19 20:08:16 [INFO] Validated directory: Images
2025-05-19 20:08:16 [INFO] Validated directory: Logs
2025-05-19 20:08:16 [INFO] Validated directory: Temporary files
2025-05-19 20:08:16 [INFO] Validated directory: Candidate images
2025-05-19 20:08:16 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:08:16 [INFO] All required files and directories validated successfully
2025-05-19 20:08:16 [INFO] Initializing application components
2025-05-19 20:08:17 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:08:17 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:08:17 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:08:17 [INFO] Room cache loaded with 3 entries
2025-05-19 20:08:17 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:08:17 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:08:17 [INFO] Database manager initialized successfully
2025-05-19 20:08:17 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:08:17 [INFO] Webcam manager initialized
2025-05-19 20:08:17 [INFO] Webcam: Starting webcam...
2025-05-19 20:08:18 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:08:18 [INFO] Webcam started successfully
2025-05-19 20:08:18 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:08:18 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:08:18 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:08:18 [INFO] Post-exam mode is enabled
2025-05-19 20:08:18 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:08:18 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:08:18 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:08:18 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:08:18 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:08:18 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:08:18 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:08:18 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:08:18 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:08:18 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:08:18 [WARNING] Failed to load company logo: Can't create control.
2025-05-19 20:08:18 [INFO] Using gray placeholder after company logo load failure
2025-05-19 20:08:18 [INFO] Added webcam feed control with default photo
2025-05-19 20:08:18 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 20:08:41 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 20:11:05 [INFO] Error handler initialized
2025-05-19 20:11:05 [INFO] Read database path from config: db
2025-05-19 20:11:06 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:11:06 [INFO] Error handler initialized
2025-05-19 20:11:06 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:11:06 [INFO] PathManager initialized successfully
2025-05-19 20:11:06 [INFO] PathManager initialized successfully
2025-05-19 20:11:06 [INFO] Validating required files and directories
2025-05-19 20:11:06 [INFO] Validated directory: Database
2025-05-19 20:11:06 [INFO] Validated directory: Images
2025-05-19 20:11:06 [INFO] Validated directory: Logs
2025-05-19 20:11:06 [INFO] Validated directory: Temporary files
2025-05-19 20:11:06 [INFO] Validated directory: Candidate images
2025-05-19 20:11:06 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:11:06 [INFO] All required files and directories validated successfully
2025-05-19 20:11:06 [INFO] Initializing application components
2025-05-19 20:11:06 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:11:06 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:11:06 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:11:06 [INFO] Room cache loaded with 3 entries
2025-05-19 20:11:06 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:11:06 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:11:06 [INFO] Database manager initialized successfully
2025-05-19 20:11:06 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:11:06 [INFO] Webcam manager initialized
2025-05-19 20:11:06 [INFO] Webcam: Starting webcam...
2025-05-19 20:11:07 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:11:07 [INFO] Webcam started successfully
2025-05-19 20:11:07 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:11:07 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:11:07 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:11:07 [INFO] Post-exam mode is enabled
2025-05-19 20:11:07 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:11:07 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:11:07 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:11:07 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:11:07 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:11:07 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:11:07 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:11:07 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:11:07 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:11:07 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:11:07 [WARNING] Failed to load company logo: Can't create control.
2025-05-19 20:11:07 [INFO] Using gray placeholder after company logo load failure
2025-05-19 20:11:07 [INFO] Added webcam feed control with default photo
2025-05-19 20:11:07 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 20:11:46 [INFO] Application exiting: Single (Code: 0)
2025-05-19 20:11:48 [INFO] Error handler initialized
2025-05-19 20:11:48 [INFO] Read database path from config: db
2025-05-19 20:11:48 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:11:48 [INFO] Error handler initialized
2025-05-19 20:11:48 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:11:49 [INFO] PathManager initialized successfully
2025-05-19 20:11:49 [INFO] Validating required files and directories
2025-05-19 20:11:49 [INFO] Validated directory: Database
2025-05-19 20:11:49 [INFO] Validated directory: Images
2025-05-19 20:11:49 [INFO] Validated directory: Logs
2025-05-19 20:11:49 [INFO] Validated directory: Temporary files
2025-05-19 20:11:49 [INFO] Validated directory: Candidate images
2025-05-19 20:11:49 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:11:49 [INFO] All required files and directories validated successfully
2025-05-19 20:11:49 [INFO] Initializing application components
2025-05-19 20:11:49 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:11:49 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:11:49 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:11:49 [INFO] Room cache loaded with 3 entries
2025-05-19 20:11:49 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:11:49 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:11:49 [INFO] Database manager initialized successfully
2025-05-19 20:11:49 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:11:49 [INFO] Webcam manager initialized
2025-05-19 20:11:49 [INFO] Webcam: Starting webcam...
2025-05-19 20:11:50 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:11:50 [INFO] Webcam started successfully
2025-05-19 20:11:50 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:11:50 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:11:50 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:11:50 [INFO] Post-exam mode is enabled
2025-05-19 20:11:50 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:11:50 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:11:50 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:11:50 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:11:50 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:11:50 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:11:50 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:11:50 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:11:50 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:11:50 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:12:06 [WARNING] Failed to load company logo: Can't create control.
2025-05-19 20:12:07 [INFO] Using gray placeholder after company logo load failure
2025-05-19 20:12:07 [INFO] Added webcam feed control with default photo
2025-05-19 20:12:07 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 20:12:11 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 20:15:49 [INFO] Error handler initialized
2025-05-19 20:15:49 [INFO] Read database path from config: db
2025-05-19 20:15:49 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:15:49 [INFO] Error handler initialized
2025-05-19 20:15:49 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:15:49 [INFO] PathManager initialized successfully
2025-05-19 20:15:49 [INFO] PathManager initialized successfully
2025-05-19 20:15:49 [INFO] Validating required files and directories
2025-05-19 20:15:49 [INFO] Validated directory: Database
2025-05-19 20:15:49 [INFO] Validated directory: Images
2025-05-19 20:15:49 [INFO] Validated directory: Logs
2025-05-19 20:15:49 [INFO] Validated directory: Temporary files
2025-05-19 20:15:49 [INFO] Validated directory: Candidate images
2025-05-19 20:15:49 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:15:49 [ERROR] Error using PathManager: This value of type "String" has no method named "Contains".
2025-05-19 20:15:49 [INFO] Validated directory: Database
2025-05-19 20:15:49 [INFO] Validated directory: Images
2025-05-19 20:15:49 [INFO] Validated directory: Logs
2025-05-19 20:15:49 [INFO] Validated directory: Temporary files
2025-05-19 20:15:49 [INFO] All required files and directories validated successfully
2025-05-19 20:15:49 [INFO] Initializing application components
2025-05-19 20:15:49 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:15:49 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:15:49 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:15:49 [INFO] Room cache loaded with 3 entries
2025-05-19 20:15:49 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:15:49 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:15:49 [INFO] Database manager initialized successfully
2025-05-19 20:15:49 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:15:49 [INFO] Webcam manager initialized
2025-05-19 20:15:49 [INFO] Webcam: Starting webcam...
2025-05-19 20:15:50 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:15:50 [INFO] Webcam started successfully
2025-05-19 20:15:50 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:15:50 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:15:50 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:15:50 [INFO] Post-exam mode is enabled
2025-05-19 20:15:50 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:15:50 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:15:50 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:15:50 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:15:50 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:15:50 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:15:50 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:15:50 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:15:50 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:15:50 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:17:20 [INFO] Error handler initialized
2025-05-19 20:17:20 [INFO] Read database path from config: db
2025-05-19 20:17:20 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:17:20 [INFO] Error handler initialized
2025-05-19 20:17:21 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:17:21 [INFO] PathManager initialized successfully
2025-05-19 20:17:21 [INFO] PathManager initialized successfully
2025-05-19 20:17:21 [INFO] Validating required files and directories
2025-05-19 20:17:21 [INFO] Validated directory: Database
2025-05-19 20:17:21 [INFO] Validated directory: Images
2025-05-19 20:17:21 [INFO] Validated directory: Logs
2025-05-19 20:17:21 [INFO] Validated directory: Temporary files
2025-05-19 20:17:21 [INFO] Validated directory: Candidate images
2025-05-19 20:17:21 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:17:21 [INFO] All required files and directories validated successfully
2025-05-19 20:17:21 [INFO] Initializing application components
2025-05-19 20:17:21 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:17:21 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:17:21 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:17:21 [INFO] Room cache loaded with 3 entries
2025-05-19 20:17:21 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:17:21 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:17:21 [INFO] Database manager initialized successfully
2025-05-19 20:17:21 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:17:21 [INFO] Webcam manager initialized
2025-05-19 20:17:21 [INFO] Webcam: Starting webcam...
2025-05-19 20:17:22 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:17:22 [INFO] Webcam started successfully
2025-05-19 20:17:22 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:17:22 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:17:22 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:17:22 [INFO] Post-exam mode is enabled
2025-05-19 20:17:22 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:17:22 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:17:22 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:17:22 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:17:22 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:17:22 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:17:22 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:17:22 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:17:22 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:17:22 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:17:22 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 20:17:22 [INFO] Added webcam feed control with default photo
2025-05-19 20:17:22 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 20:18:42 [INFO] Found seat assignment for 9351: F1-R1-S5
2025-05-19 20:19:43 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 20:24:53 [INFO] Error handler initialized
2025-05-19 20:24:53 [INFO] Read database path from config: db
2025-05-19 20:24:53 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:24:53 [INFO] Error handler initialized
2025-05-19 20:24:53 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:24:53 [INFO] PathManager initialized successfully
2025-05-19 20:24:53 [INFO] PathManager initialized successfully
2025-05-19 20:24:53 [INFO] Validating required files and directories
2025-05-19 20:24:53 [INFO] Validated directory: Database
2025-05-19 20:24:53 [INFO] Validated directory: Images
2025-05-19 20:24:53 [INFO] Validated directory: Logs
2025-05-19 20:24:53 [INFO] Validated directory: Temporary files
2025-05-19 20:24:53 [INFO] Validated directory: Candidate images
2025-05-19 20:24:53 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:24:53 [INFO] All required files and directories validated successfully
2025-05-19 20:24:53 [INFO] Initializing application components
2025-05-19 20:24:53 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:24:53 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:24:53 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:24:53 [INFO] Room cache loaded with 3 entries
2025-05-19 20:24:53 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:24:53 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:24:53 [INFO] Database manager initialized successfully
2025-05-19 20:24:53 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:24:54 [INFO] Webcam manager initialized
2025-05-19 20:24:54 [INFO] Webcam: Starting webcam...
2025-05-19 20:24:54 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:24:54 [INFO] Webcam started successfully
2025-05-19 20:24:54 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:24:54 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:24:54 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:24:54 [INFO] Post-exam mode is enabled
2025-05-19 20:24:54 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:24:54 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:24:54 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:24:54 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:24:55 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:24:55 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:24:55 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:24:55 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:24:55 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:24:55 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:24:55 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 20:24:55 [INFO] Added webcam feed control with default photo
2025-05-19 20:24:55 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 20:25:06 [INFO] Found seat assignment for 9351: F1-R1-S5
2025-05-19 20:25:16 [INFO] No seat assignment found for 9353
2025-05-19 20:25:23 [INFO] No seat assignment found for 9355
2025-05-19 20:25:40 [INFO] No seat assignment found for 9356
2025-05-19 20:25:47 [INFO] No seat assignment found for 9357
2025-05-19 20:25:55 [INFO] No seat assignment found for 9359
2025-05-19 20:27:20 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 20:27:50 [INFO] Error handler initialized
2025-05-19 20:27:50 [INFO] Read database path from config: db
2025-05-19 20:27:50 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:27:50 [INFO] Error handler initialized
2025-05-19 20:27:50 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:27:50 [INFO] PathManager initialized successfully
2025-05-19 20:27:50 [INFO] PathManager initialized successfully
2025-05-19 20:27:50 [INFO] Validating required files and directories
2025-05-19 20:27:50 [INFO] Validated directory: Database
2025-05-19 20:27:50 [INFO] Validated directory: Images
2025-05-19 20:27:50 [INFO] Validated directory: Logs
2025-05-19 20:27:50 [INFO] Validated directory: Temporary files
2025-05-19 20:27:50 [INFO] Validated directory: Candidate images
2025-05-19 20:27:50 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:27:50 [INFO] All required files and directories validated successfully
2025-05-19 20:27:50 [INFO] Initializing application components
2025-05-19 20:27:50 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:27:50 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:27:50 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:27:50 [INFO] Room cache loaded with 3 entries
2025-05-19 20:27:50 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:27:50 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:27:50 [INFO] Database manager initialized successfully
2025-05-19 20:27:50 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:27:50 [INFO] Webcam manager initialized
2025-05-19 20:27:50 [INFO] Webcam: Starting webcam...
2025-05-19 20:27:51 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:27:51 [INFO] Webcam started successfully
2025-05-19 20:27:52 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:27:52 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:27:52 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:27:52 [INFO] Post-exam mode is enabled
2025-05-19 20:27:52 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:27:52 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:27:52 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:27:52 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:27:52 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:27:52 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:27:52 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:27:52 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:27:52 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:27:52 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:27:52 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 20:27:52 [INFO] Added webcam feed control with default photo
2025-05-19 20:27:52 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 20:27:58 [INFO] No seat assignment found for 9359
2025-05-19 20:28:27 [INFO] No seat assignment found for 9359
2025-05-19 20:28:40 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 20:31:11 [INFO] Error handler initialized
2025-05-19 20:31:11 [INFO] Read database path from config: db
2025-05-19 20:31:11 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:31:11 [INFO] Error handler initialized
2025-05-19 20:31:11 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:31:11 [INFO] PathManager initialized successfully
2025-05-19 20:31:11 [INFO] PathManager initialized successfully
2025-05-19 20:31:11 [INFO] Validating required files and directories
2025-05-19 20:31:11 [INFO] Validated directory: Database
2025-05-19 20:31:11 [INFO] Validated directory: Images
2025-05-19 20:31:11 [INFO] Validated directory: Logs
2025-05-19 20:31:11 [INFO] Validated directory: Temporary files
2025-05-19 20:31:11 [INFO] Validated directory: Candidate images
2025-05-19 20:31:11 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:31:11 [INFO] All required files and directories validated successfully
2025-05-19 20:31:11 [INFO] Initializing application components
2025-05-19 20:31:11 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:31:11 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:31:11 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:31:11 [INFO] Room cache loaded with 3 entries
2025-05-19 20:31:11 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:31:11 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:31:11 [INFO] Database manager initialized successfully
2025-05-19 20:31:11 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:31:11 [INFO] Webcam manager initialized
2025-05-19 20:31:11 [INFO] Webcam: Starting webcam...
2025-05-19 20:31:12 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:31:12 [INFO] Webcam started successfully
2025-05-19 20:31:12 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:31:12 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:31:12 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:31:12 [INFO] Post-exam mode is enabled
2025-05-19 20:31:12 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:31:12 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:31:12 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:31:12 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:31:12 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:31:12 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:31:12 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:31:12 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:31:12 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:31:12 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:31:12 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 20:31:13 [INFO] Added webcam feed control with default photo
2025-05-19 20:31:13 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 20:31:20 [INFO] No seat assignment found for 9359
2025-05-19 20:32:43 [INFO] Application exiting: Single (Code: 0)
2025-05-19 20:32:44 [INFO] Error handler initialized
2025-05-19 20:32:44 [INFO] Read database path from config: db
2025-05-19 20:32:44 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:32:44 [INFO] Error handler initialized
2025-05-19 20:32:44 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:32:44 [INFO] PathManager initialized successfully
2025-05-19 20:32:44 [INFO] PathManager initialized successfully
2025-05-19 20:32:44 [INFO] Validating required files and directories
2025-05-19 20:32:44 [INFO] Validated directory: Database
2025-05-19 20:32:44 [INFO] Validated directory: Images
2025-05-19 20:32:44 [INFO] Validated directory: Logs
2025-05-19 20:32:44 [INFO] Validated directory: Temporary files
2025-05-19 20:32:44 [INFO] Validated directory: Candidate images
2025-05-19 20:32:44 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:32:44 [INFO] All required files and directories validated successfully
2025-05-19 20:32:44 [INFO] Initializing application components
2025-05-19 20:32:44 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:32:44 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:32:44 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:32:44 [INFO] Room cache loaded with 3 entries
2025-05-19 20:32:44 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:32:44 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:32:44 [INFO] Database manager initialized successfully
2025-05-19 20:32:44 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:32:44 [INFO] Webcam manager initialized
2025-05-19 20:32:44 [INFO] Webcam: Starting webcam...
2025-05-19 20:32:45 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:32:45 [INFO] Webcam started successfully
2025-05-19 20:32:45 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:32:45 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:32:45 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:32:45 [INFO] Post-exam mode is enabled
2025-05-19 20:32:45 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:32:45 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:32:45 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:32:45 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:32:45 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:32:45 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:32:45 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:32:45 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:32:45 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:32:45 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:32:45 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 20:32:45 [INFO] Added webcam feed control with default photo
2025-05-19 20:32:45 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 20:32:51 [INFO] No seat assignment found for 9359
2025-05-19 20:34:39 [INFO] Application exiting: Single (Code: 0)
2025-05-19 20:34:39 [INFO] Error handler initialized
2025-05-19 20:34:39 [INFO] Read database path from config: db
2025-05-19 20:34:39 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:34:39 [INFO] Error handler initialized
2025-05-19 20:34:39 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:34:39 [INFO] PathManager initialized successfully
2025-05-19 20:34:39 [INFO] PathManager initialized successfully
2025-05-19 20:34:39 [INFO] Validating required files and directories
2025-05-19 20:34:39 [INFO] Validated directory: Database
2025-05-19 20:34:39 [INFO] Validated directory: Images
2025-05-19 20:34:39 [INFO] Validated directory: Logs
2025-05-19 20:34:39 [INFO] Validated directory: Temporary files
2025-05-19 20:34:39 [INFO] Validated directory: Candidate images
2025-05-19 20:34:39 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:34:40 [INFO] All required files and directories validated successfully
2025-05-19 20:34:40 [INFO] Initializing application components
2025-05-19 20:34:40 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:34:40 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:34:40 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:34:40 [INFO] Room cache loaded with 3 entries
2025-05-19 20:34:40 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:34:40 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:34:40 [INFO] Database manager initialized successfully
2025-05-19 20:34:40 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:34:40 [INFO] Webcam manager initialized
2025-05-19 20:34:40 [INFO] Webcam: Starting webcam...
2025-05-19 20:34:40 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:34:40 [INFO] Webcam started successfully
2025-05-19 20:34:40 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:34:40 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:34:40 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:34:40 [INFO] Post-exam mode is enabled
2025-05-19 20:34:40 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:34:40 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:34:40 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:34:40 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:34:40 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:34:40 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:34:40 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:34:40 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:34:40 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:34:40 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:34:40 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 20:34:41 [INFO] Added webcam feed control with default photo
2025-05-19 20:34:41 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 20:34:47 [INFO] No seat assignment found for 9359
2025-05-19 20:35:15 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 20:36:12 [INFO] Error handler initialized
2025-05-19 20:36:12 [INFO] Read database path from config: db
2025-05-19 20:36:12 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:36:12 [INFO] Error handler initialized
2025-05-19 20:36:12 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:36:12 [INFO] PathManager initialized successfully
2025-05-19 20:36:12 [INFO] PathManager initialized successfully
2025-05-19 20:36:12 [INFO] Validating required files and directories
2025-05-19 20:36:12 [INFO] Validated directory: Database
2025-05-19 20:36:12 [INFO] Validated directory: Images
2025-05-19 20:36:12 [INFO] Validated directory: Logs
2025-05-19 20:36:12 [INFO] Validated directory: Temporary files
2025-05-19 20:36:12 [INFO] Validated directory: Candidate images
2025-05-19 20:36:12 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:36:12 [INFO] All required files and directories validated successfully
2025-05-19 20:36:12 [INFO] Initializing application components
2025-05-19 20:36:12 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:36:12 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:36:12 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:36:12 [INFO] Room cache loaded with 3 entries
2025-05-19 20:36:12 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:36:12 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:36:12 [INFO] Database manager initialized successfully
2025-05-19 20:36:12 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:36:12 [INFO] Webcam manager initialized
2025-05-19 20:36:12 [INFO] Webcam: Starting webcam...
2025-05-19 20:36:13 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:36:13 [INFO] Webcam started successfully
2025-05-19 20:36:13 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:36:13 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:36:13 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:36:13 [INFO] Post-exam mode is enabled
2025-05-19 20:36:13 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:36:13 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:36:13 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:36:13 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:36:13 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:36:13 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:36:13 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:36:13 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:36:13 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:36:13 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:36:14 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 20:36:14 [INFO] Added webcam feed control with default photo
2025-05-19 20:36:14 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 20:36:19 [INFO] No seat assignment found for 9359
2025-05-19 20:36:28 [INFO] Application exiting: Exit (Code: 0)
2025-05-19 20:37:02 [INFO] Error handler initialized
2025-05-19 20:37:02 [INFO] Read database path from config: db
2025-05-19 20:37:02 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 20:37:02 [INFO] Error handler initialized
2025-05-19 20:37:02 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 20:37:02 [INFO] PathManager initialized successfully
2025-05-19 20:37:02 [INFO] PathManager initialized successfully
2025-05-19 20:37:02 [INFO] Validating required files and directories
2025-05-19 20:37:02 [INFO] Validated directory: Database
2025-05-19 20:37:02 [INFO] Validated directory: Images
2025-05-19 20:37:02 [INFO] Validated directory: Logs
2025-05-19 20:37:02 [INFO] Validated directory: Temporary files
2025-05-19 20:37:02 [INFO] Validated directory: Candidate images
2025-05-19 20:37:02 [INFO] Validated directory: Fingerprint templates
2025-05-19 20:37:02 [INFO] All required files and directories validated successfully
2025-05-19 20:37:02 [INFO] Initializing application components
2025-05-19 20:37:02 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 20:37:02 [INFO] Hardware cache loaded with 10 entries
2025-05-19 20:37:02 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 20:37:02 [INFO] Room cache loaded with 3 entries
2025-05-19 20:37:02 [INFO] Loading seat assignments for date: 20250519
2025-05-19 20:37:02 [INFO] Loaded 1 seat assignments for today
2025-05-19 20:37:02 [INFO] Database manager initialized successfully
2025-05-19 20:37:02 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 20:37:02 [INFO] Webcam manager initialized
2025-05-19 20:37:02 [INFO] Webcam: Starting webcam...
2025-05-19 20:37:03 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 20:37:03 [INFO] Webcam started successfully
2025-05-19 20:37:03 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 20:37:03 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 20:37:03 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 20:37:03 [INFO] Post-exam mode is enabled
2025-05-19 20:37:03 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 20:37:03 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 20:37:03 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 20:37:03 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 20:37:03 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 20:37:03 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 20:37:03 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 20:37:03 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 20:37:03 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 20:37:03 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 20:37:03 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 20:37:03 [INFO] Added webcam feed control with default photo
2025-05-19 20:37:03 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 20:37:09 [INFO] No seat assignment found for 9359
2025-05-19 22:27:01 [INFO] Error handler initialized
2025-05-19 22:27:01 [INFO] Read database path from config: db
2025-05-19 22:27:01 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 22:27:01 [INFO] Error handler initialized
2025-05-19 22:27:01 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 22:27:01 [INFO] PathManager initialized successfully
2025-05-19 22:27:01 [INFO] PathManager initialized successfully
2025-05-19 22:27:01 [INFO] Validating required files and directories
2025-05-19 22:27:01 [INFO] Validated directory: Database
2025-05-19 22:27:01 [INFO] Validated directory: Images
2025-05-19 22:27:01 [INFO] Validated directory: Logs
2025-05-19 22:27:01 [INFO] Validated directory: Temporary files
2025-05-19 22:27:01 [INFO] Validated directory: Candidate images
2025-05-19 22:27:01 [INFO] Validated directory: Fingerprint templates
2025-05-19 22:27:01 [INFO] All required files and directories validated successfully
2025-05-19 22:27:01 [INFO] Initializing application components
2025-05-19 22:27:01 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 22:27:01 [INFO] Hardware cache loaded with 10 entries
2025-05-19 22:27:01 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 22:27:01 [INFO] Room cache loaded with 3 entries
2025-05-19 22:27:01 [INFO] Loading seat assignments for date: 20250519
2025-05-19 22:27:01 [INFO] Loaded 1 seat assignments for today
2025-05-19 22:27:01 [INFO] Database manager initialized successfully
2025-05-19 22:27:01 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 22:27:01 [INFO] Webcam: Starting webcam...
2025-05-19 22:27:02 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 22:27:02 [INFO] Webcam started successfully
2025-05-19 22:27:02 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 22:27:02 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 22:27:02 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 22:27:02 [INFO] Post-exam mode is enabled
2025-05-19 22:27:02 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 22:27:02 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 22:27:02 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 22:27:02 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 22:27:02 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 22:27:02 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 22:27:02 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 22:27:02 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 22:27:02 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 22:27:02 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 22:27:02 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-19 22:27:03 [INFO] Added webcam feed control with default photo
2025-05-19 22:27:03 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-19 22:27:49 [INFO] Application exiting: Exit (Code: 0)

#Requires AutoHotkey v2.0

; CSV Export functionality for WinCBT-Biometric
; This module handles exporting candidate data to CSV files

; Include the CSV importer for shared functionality
#Include csv_importer.ahk

; Create CSV Exporter class
class CSVExporter {
    ; Configuration
    exportDir := A_ScriptDir "\export"
    candidatesPath := ""
    
    ; Field mappings for CSV export
    defaultExportFields := [
        {field: "RollNumber", header: "RollNumber"},
        {field: "Name", header: "Name"},
        {field: "FatherName", header: "FatherName"},
        {field: "DateOfBirth", header: "DOB"},
        {field: "Email", header: "Email"},
        {field: "Mobile", header: "Mobile"},
        {field: "Gender", header: "Gender"},
        {field: "CenterID", header: "CenterID"},
        {field: "ExamID", header: "ExamID"},
        {field: "StudentID", header: "StudentID"},
        {field: "Status", header: "Status"},
        {field: "Special", header: "Special"},
        {field: "PhotoStatus", header: "PhotoStatus"},
        {field: "BiometricStatus", header: "BiometricStatus"},
        {field: "FingerprintStatus", header: "FingerprintStatus"},
        {field: "RightFingerprintStatus", header: "RightFingerprintStatus"},
        {field: "SignatureStatus", header: "SignatureStatus"},
        {field: "ThumbPreference", header: "ThumbPreference"}
    ]
    
    ; Constructor - Initialize directories
    __New() {
        ; Create export directory if it doesn't exist
        if (!DirExist(this.exportDir)) {
            DirCreate(this.exportDir)
        }
        
        ; Set the candidates path from the global variable
        global CSV_CANDIDATES_PATH
        this.candidatesPath := CSV_CANDIDATES_PATH
    }
    
    ; Export candidates to CSV file
    ExportCandidatesToCSV(csvFile, options := "") {
        ; Initialize results
        results := {
            totalCandidates: 0,
            exported: 0,
            errors: []
        }
        
        ; Set default options
        if (!IsObject(options)) {
            options := {}
        }
        options.delimiter := options.HasOwnProp("delimiter") ? options.delimiter : ","
        options.includeHeaders := options.HasOwnProp("includeHeaders") ? options.includeHeaders : true
        options.fields := options.HasOwnProp("fields") ? options.fields : this.defaultExportFields
        
        try {
            ; Get all candidates
            candidates := this.GetAllCandidates(options.HasOwnProp("filter") ? options.filter : "")
            
            results.totalCandidates := candidates.Length
            
            ; Create CSV content
            csvContent := ""
            
            ; Add headers if requested
            if (options.includeHeaders) {
                headers := []
                for field in options.fields {
                    headers.Push(field.header)
                }
                csvContent := StrJoin(headers, options.delimiter) "`n"
            }
            
            ; Add candidate data
            for candidate in candidates {
                row := []
                for field in options.fields {
                    row.Push(candidate.HasOwnProp(field.field) ? candidate[field.field] : "")
                }
                csvContent .= StrJoin(row, options.delimiter) "`n"
                results.exported++
            }
            
            ; Write to file
            FileOpen(csvFile, "w").Write(csvContent)
            
            return results
        } catch Error as e {
            results.errors.Push("Error exporting candidates: " e.Message)
            return results
        }
    }
    
    ; Get all candidates
    GetAllCandidates(filter := "") {
        candidates := []
        
        try {
            ; Read all sections from the INI file
            fileContent := FileRead(this.candidatesPath)
            sections := []
            
            ; Parse the file to extract section names
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    sections.Push(match[1])
                }
            }
            
            ; Process each candidate
            for rollNumber in sections {
                ; Read candidate data
                candidate := {
                    RollNumber: rollNumber,
                    Name: IniRead(this.candidatesPath, rollNumber, "Name", ""),
                    FatherName: IniRead(this.candidatesPath, rollNumber, "FatherName", ""),
                    DateOfBirth: IniRead(this.candidatesPath, rollNumber, "DateOfBirth", ""),
                    Email: IniRead(this.candidatesPath, rollNumber, "Email", ""),
                    Mobile: IniRead(this.candidatesPath, rollNumber, "Mobile", ""),
                    Gender: IniRead(this.candidatesPath, rollNumber, "Gender", ""),
                    CenterID: IniRead(this.candidatesPath, rollNumber, "CenterID", ""),
                    ExamID: IniRead(this.candidatesPath, rollNumber, "ExamID", ""),
                    StudentID: IniRead(this.candidatesPath, rollNumber, "StudentID", ""),
                    Status: IniRead(this.candidatesPath, rollNumber, "Status", ""),
                    Special: IniRead(this.candidatesPath, rollNumber, "Special", "0"),
                    PhotoStatus: IniRead(this.candidatesPath, rollNumber, "PhotoStatus", ""),
                    BiometricStatus: IniRead(this.candidatesPath, rollNumber, "BiometricStatus", ""),
                    FingerprintStatus: IniRead(this.candidatesPath, rollNumber, "FingerprintStatus", ""),
                    RightFingerprintStatus: IniRead(this.candidatesPath, rollNumber, "RightFingerprintStatus", ""),
                    SignatureStatus: IniRead(this.candidatesPath, rollNumber, "SignatureStatus", ""),
                    ThumbPreference: IniRead(this.candidatesPath, rollNumber, "ThumbPreference", "Both")
                }
                
                ; Apply filter if provided
                if (IsObject(filter)) {
                    if (filter(candidate)) {
                        candidates.Push(candidate)
                    }
                } else {
                    candidates.Push(candidate)
                }
            }
        } catch Error as e {
            throw Error("Error getting candidates: " e.Message)
        }
        
        return candidates
    }
}

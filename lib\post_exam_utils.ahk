; post_exam_utils.ahk
; Utility functions for post-exam verification mode

/**
 * Safely reads an integer value from an INI file
 * @param filePath The path to the INI file
 * @param section The section in the INI file
 * @param key The key to read
 * @param defaultValue The default value to return if the key is not found or is not a valid integer
 * @return The integer value, or the default value if not found or not a valid integer
 */
PostExamSafeReadInteger(filePath, section, key, defaultValue) {
    try {
        value := IniRead(filePath, section, key, defaultValue)
        return Integer(value)
    } catch {
        return Integer(defaultValue)
    }
}

/**
 * Determines if a candidate should be in post-exam verification mode
 * @param rollNumber The roll number of the candidate
 * @return True if the candidate should be in post-exam verification mode, false otherwise
 */
IsPostExamVerification(rollNumber) {
    try {
        ; Check if the candidate has a seat assigned
        assignedSeat := g_dbManager.GetCandidateSeat(rollNumber)
        if (assignedSeat == "") {
            ErrorHandler.LogMessage("INFO", "IsPostExamVerification: Candidate " rollNumber " has no seat assigned, not in post-exam mode")
            return false
        }

        ; Check if the candidate has started the exam
        ; Get the memory file path for this candidate
        macAddress := GetMACAddress()
        memoryFile := A_ScriptDir "\tmp\" macAddress "\" rollNumber "_memory.ini"

        ; If memory file doesn't exist, try using global timing as fallback
        if (!FileExist(memoryFile)) {
            ErrorHandler.LogMessage("INFO", "IsPostExamVerification: Memory file not found for " rollNumber ", checking global timing")
            return FallbackToGlobalTiming()
        }

        ; Read exam progress from memory file
        startTime := IniRead(memoryFile, "Timer", "StartTime", "")
        remainingTime := IniRead(memoryFile, "Timer", "RemainingTime", "")
        completionPercentage := IniRead(memoryFile, "Progress", "CompletionPercentage", "0")

        ; If no start time, try using global timing as fallback
        if (startTime == "") {
            ErrorHandler.LogMessage("INFO", "IsPostExamVerification: No start time found for " rollNumber ", checking global timing")
            return FallbackToGlobalTiming()
        }

        ; Convert completion percentage to number
        completionPercentage := Number(completionPercentage)

        ; If completion percentage is >= 50%, consider it post-exam
        if (completionPercentage >= 50) {
            ErrorHandler.LogMessage("INFO", "IsPostExamVerification: Candidate " rollNumber " has completed " completionPercentage "% of exam, entering post-exam mode")
            return true
        }

        ; If we get here, the candidate has started the exam but hasn't completed enough to be in post-exam mode
        ErrorHandler.LogMessage("INFO", "IsPostExamVerification: Candidate " rollNumber " has only completed " completionPercentage "% of exam, not in post-exam mode")
        return false
    } catch as err {
        ErrorHandler.LogMessage("ERROR", "Error in IsPostExamVerification: " err.Message)
        ; Try global timing as a last resort
        try {
            ErrorHandler.LogMessage("INFO", "IsPostExamVerification: Error occurred, trying global timing as fallback")
            return FallbackToGlobalTiming()
        } catch {
            return false
        }
    }
}

/**
 * Updates the post-exam verification status for a candidate
 * @param rollNumber The roll number of the candidate
 * @param biometricType The type of biometric (Photo, Fingerprint, RightFingerprint, Signature)
 * @param status The status to set (Verified, Failed, etc.)
 * @return True if successful, false otherwise
 */
UpdatePostExamStatus(rollNumber, biometricType, status) {
    try {
        ; Get database file path
        candidatesPath := PathManager.GetDatabaseFilePath("Candidates")

        ; Determine the field name based on biometric type
        fieldName := "PostExam" biometricType "Status"

        ; Write the status to the database
        IniWrite(status, candidatesPath, rollNumber, fieldName)

        ; Log the update
        ErrorHandler.LogMessage("INFO", "Updated post-exam status for " rollNumber ": " fieldName " = " status)

        ; Check if all post-exam verifications are complete
        CheckPostExamVerifications(rollNumber)

        return true
    } catch as err {
        ErrorHandler.LogMessage("ERROR", "Error updating post-exam status: " err.Message)
        return false
    }
}

/**
 * Checks if all required post-exam verifications are complete for a candidate
 * @param rollNumber The roll number of the candidate
 * @return True if all verifications are complete, false otherwise
 */
CheckPostExamVerifications(rollNumber) {
    try {
        ; Get database file path
        candidatesPath := PathManager.GetDatabaseFilePath("Candidates")

        ; Read verification settings
        configFile := PathManager.ConfigFile
        signatureVerificationEnabled := PostExamSafeReadInteger(configFile, "Verification", "SignatureVerification", "1")
        rightThumbprintVerificationEnabled := PostExamSafeReadInteger(configFile, "Verification", "RightThumbprintVerification", "1")

        ; Read current status values
        photoStatus := IniRead(candidatesPath, rollNumber, "PostExamPhotoStatus", "")
        fingerprintStatus := IniRead(candidatesPath, rollNumber, "PostExamFingerprintStatus", "")
        rightFingerprintStatus := IniRead(candidatesPath, rollNumber, "PostExamRightFingerprintStatus", "")
        signatureStatus := IniRead(candidatesPath, rollNumber, "PostExamSignatureStatus", "")

        ; Check if all required verifications are complete
        allComplete := true

        ; Photo is always required
        if (photoStatus != "Verified") {
            allComplete := false
        }

        ; Left fingerprint is always required
        if (fingerprintStatus != "Verified") {
            allComplete := false
        }

        ; Right fingerprint is required if enabled
        if (rightThumbprintVerificationEnabled && rightFingerprintStatus != "Verified") {
            allComplete := false
        }

        ; Signature is required if enabled
        if (signatureVerificationEnabled && signatureStatus != "Verified") {
            allComplete := false
        }

        ; If all required verifications are complete, update the overall status
        if (allComplete) {
            IniWrite("Verified", candidatesPath, rollNumber, "PostExamBiometricStatus")
            ErrorHandler.LogMessage("INFO", "All post-exam verifications complete for " rollNumber ", updated PostExamBiometricStatus to Verified")
        } else {
            IniWrite("Incomplete", candidatesPath, rollNumber, "PostExamBiometricStatus")
            ErrorHandler.LogMessage("INFO", "Post-exam verifications incomplete for " rollNumber ", updated PostExamBiometricStatus to Incomplete")
        }

        return allComplete
    } catch as err {
        ErrorHandler.LogMessage("ERROR", "Error checking post-exam verifications: " err.Message)
        return false
    }
}

/**
 * Fallback to global exam timing if individual timing not available
 * @return True if post-exam verification should be used based on global timing, False otherwise
 */
FallbackToGlobalTiming() {
    try {
        configPath := PathManager.GetDatabasePath() . "\config.ini"

        examStartTime := IniRead(configPath, "Exam", "StartTime", "000000")
        examEndTime := IniRead(configPath, "Exam", "EndTime", "235900")
        currentTime := FormatTime(, "HHmmss")

        ErrorHandler.LogMessage("INFO", "Global exam times - Start: " examStartTime ", End: " examEndTime ", Current: " currentTime)

        ; Convert times to seconds since midnight for easier comparison
        startSeconds := (SubStr(examStartTime, 1, 2) * 3600) + (SubStr(examStartTime, 3, 2) * 60) + SubStr(examStartTime, 5, 2)
        endSeconds := (SubStr(examEndTime, 1, 2) * 3600) + (SubStr(examEndTime, 3, 2) * 60) + SubStr(examEndTime, 5, 2)
        currentSeconds := (SubStr(currentTime, 1, 2) * 3600) + (SubStr(currentTime, 3, 2) * 60) + SubStr(currentTime, 5, 2)

        ; Calculate exam duration and elapsed time
        examDuration := endSeconds - startSeconds
        elapsedTime := currentSeconds - startSeconds

        ; Handle case where current time is before start time
        if (elapsedTime < 0) {
            ErrorHandler.LogMessage("INFO", "Current time is before exam start time, not in post-exam mode")
            return false
        }

        ; Handle case where exam spans midnight
        if (endSeconds < startSeconds) {
            examDuration := (24 * 3600) - startSeconds + endSeconds

            ; If current time is after midnight but before end time
            if (currentSeconds < endSeconds) {
                elapsedTime := (24 * 3600) - startSeconds + currentSeconds
            }
        }

        ; Calculate progress percentage
        examProgress := (elapsedTime / examDuration) * 100

        ErrorHandler.LogMessage("INFO", "Global exam progress: " examProgress "% (Elapsed: " elapsedTime "s, Duration: " examDuration "s)")

        ; Return true if progress > 50%
        isPostExam := (examProgress > 50)
        ErrorHandler.LogMessage("INFO", "Is post-exam mode based on global timing: " (isPostExam ? "Yes" : "No"))
        return isPostExam
    } catch as err {
        ErrorHandler.LogMessage("ERROR", "Error in global timing calculation: " err.Message)
        ; Default to false if there's an error
        return false
    }
}

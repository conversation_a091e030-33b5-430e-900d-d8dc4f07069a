=== WinCBT-Biometric Error Log ===
Started: 2025-05-19 14:20:02
----------------------------------------
2025-05-19 14:20:02 [INFO] Error handler initialized
2025-05-19 14:20:02 [INFO] Read database path from config: db
2025-05-19 14:20:02 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 14:20:02 [INFO] Created Temporary files directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\tmp
2025-05-19 14:20:02 [INFO] Error handler initialized
2025-05-19 14:20:02 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 14:20:02 [ERROR] Cannot access Configuration file: Invalid option.
2025-05-19 14:20:02 [ERROR] Cannot access Configuration file at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\config.ini
2025-05-19 14:20:10 [ERROR] Cannot access Database configuration: Invalid option.
2025-05-19 14:20:10 [ERROR] Cannot access Database configuration at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\config.ini
2025-05-19 14:20:12 [ERROR] Cannot access Candidates database: Invalid option.
2025-05-19 14:20:12 [ERROR] Cannot access Candidates database at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\candidates.ini
2025-05-19 14:20:13 [ERROR] Cannot access Hardware configuration: Invalid option.
2025-05-19 14:20:13 [ERROR] Cannot access Hardware configuration at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:20:15 [ERROR] Cannot access Rooms configuration: Invalid option.
2025-05-19 14:20:15 [ERROR] Cannot access Rooms configuration at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:20:20 [ERROR] Cannot access Seat assignments: Invalid option.
2025-05-19 14:20:20 [ERROR] Cannot access Seat assignments at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\seat_assignments.ini
2025-05-19 14:20:23 [ERROR] Cannot access FFmpeg executable: Invalid option.
2025-05-19 14:20:23 [ERROR] Cannot access FFmpeg executable at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\bin\ffmpeg.exe
2025-05-19 14:20:38 [ERROR] Cannot access Default photo image: Invalid option.
2025-05-19 14:20:38 [ERROR] Cannot access Default photo image at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\default_photo.png
2025-05-19 14:20:40 [ERROR] Cannot access Default fingerprint image: Invalid option.
2025-05-19 14:20:40 [ERROR] Cannot access Default fingerprint image at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\default_fingerprint.png
2025-05-19 14:20:42 [ERROR] Cannot access Default signature image: Invalid option.
2025-05-19 14:20:42 [ERROR] Cannot access Default signature image at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\default_signature.png
2025-05-19 14:20:45 [ERROR] Cannot access Gray placeholder image: Invalid option.
2025-05-19 14:20:45 [ERROR] Cannot access Gray placeholder image at E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\img\gray.png
2025-05-19 14:20:47 [ERROR] Some required files or directories are missing. The application may not function correctly.
2025-05-19 14:20:51 [INFO] Initializing application components
2025-05-19 14:20:51 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:20:51 [INFO] Hardware cache loaded with 10 entries
2025-05-19 14:20:51 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:20:51 [INFO] Room cache loaded with 3 entries
2025-05-19 14:20:51 [INFO] Loading seat assignments for date: 20250519
2025-05-19 14:20:51 [INFO] No seat assignments found for today (20250519)
2025-05-19 14:20:51 [INFO] Database manager initialized successfully
2025-05-19 14:20:51 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 14:20:51 [INFO] Webcam manager initialized
2025-05-19 14:20:51 [INFO] Webcam: Starting webcam...
2025-05-19 14:20:53 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 14:20:53 [INFO] Webcam started successfully
2025-05-19 14:20:53 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 14:20:53 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 14:20:53 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 14:20:53 [INFO] Post-exam mode is enabled
2025-05-19 14:20:53 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 14:20:53 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 14:20:53 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 14:20:53 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 14:20:53 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 14:20:53 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 14:20:53 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 14:20:53 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 14:20:53 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 14:20:53 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 14:20:53 [INFO] Config: Paths.dbPath = db
2025-05-19 14:21:10 [INFO] Error handler initialized
2025-05-19 14:21:10 [INFO] Read database path from config: db
2025-05-19 14:21:10 [INFO] Using database path: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\
2025-05-19 14:21:10 [INFO] Created Fingerprint templates directory: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\fpt\
2025-05-19 14:21:10 [INFO] Created Temporary files directory: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\tmp
2025-05-19 14:21:10 [INFO] Created empty Database configuration file: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\config.ini
2025-05-19 14:21:10 [INFO] Created empty Candidates database file: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\candidates.ini
2025-05-19 14:21:10 [INFO] Created empty Hardware configuration file: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:21:10 [INFO] Created empty Rooms configuration file: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:21:10 [INFO] Created empty Seat assignments file: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\seat_assignments.ini
2025-05-19 14:21:10 [INFO] Error handler initialized
2025-05-19 14:21:10 [INFO] Starting WinCBT-Biometric (v1.4.1 Build 20250520)
2025-05-19 14:21:10 [WARNING] Temporary files directory not found at: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\tmp
2025-05-19 14:21:10 [INFO] Created directory: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\tmp
2025-05-19 14:21:10 [ERROR] Cannot access Configuration file: Invalid option.
2025-05-19 14:21:10 [ERROR] Cannot access Configuration file at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\config.ini
2025-05-19 14:21:25 [ERROR] Cannot access Database configuration: Invalid option.
2025-05-19 14:21:25 [ERROR] Cannot access Database configuration at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\config.ini
2025-05-19 14:21:29 [ERROR] Cannot access Candidates database: Invalid option.
2025-05-19 14:21:29 [ERROR] Cannot access Candidates database at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\candidates.ini
2025-05-19 14:21:31 [ERROR] Cannot access Hardware configuration: Invalid option.
2025-05-19 14:21:31 [ERROR] Cannot access Hardware configuration at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:21:31 [ERROR] Cannot access Rooms configuration: Invalid option.
2025-05-19 14:21:31 [ERROR] Cannot access Rooms configuration at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:21:32 [ERROR] Cannot access Seat assignments: Invalid option.
2025-05-19 14:21:32 [ERROR] Cannot access Seat assignments at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\seat_assignments.ini
2025-05-19 14:21:33 [ERROR] Cannot access FFmpeg executable: Invalid option.
2025-05-19 14:21:33 [ERROR] Cannot access FFmpeg executable at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\bin\ffmpeg.exe
2025-05-19 14:21:34 [ERROR] Cannot access Default photo image: Invalid option.
2025-05-19 14:21:34 [ERROR] Cannot access Default photo image at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\img\default_photo.png
2025-05-19 14:21:34 [ERROR] Cannot access Default fingerprint image: Invalid option.
2025-05-19 14:21:34 [ERROR] Cannot access Default fingerprint image at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\img\default_fingerprint.png
2025-05-19 14:21:35 [ERROR] Cannot access Default signature image: Invalid option.
2025-05-19 14:21:35 [ERROR] Cannot access Default signature image at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\img\default_signature.png
2025-05-19 14:21:36 [ERROR] Cannot access Gray placeholder image: Invalid option.
2025-05-19 14:21:36 [ERROR] Cannot access Gray placeholder image at D:\MY\Sync\WORK\CBT\WinCBT-Biometric\img\gray.png
2025-05-19 14:21:37 [ERROR] Some required files or directories are missing. The application may not function correctly.
2025-05-19 14:21:40 [INFO] Initializing application components
2025-05-19 14:21:40 [INFO] Loading hardware cache from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-19 14:21:40 [INFO] Hardware cache loaded with 0 entries
2025-05-19 14:21:40 [INFO] Loading room cache from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-19 14:21:40 [INFO] Room cache loaded with 0 entries
2025-05-19 14:21:40 [INFO] Loading seat assignments for date: 20250519
2025-05-19 14:21:40 [INFO] No seat assignments found for today (20250519)
2025-05-19 14:21:40 [INFO] Database manager initialized successfully
2025-05-19 14:21:40 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-19 14:21:40 [INFO] Webcam manager initialized
2025-05-19 14:21:40 [INFO] Webcam: Starting webcam...
2025-05-19 14:21:41 [INFO] Webcam: Webcam active (Index: 0)
2025-05-19 14:21:41 [INFO] Webcam started successfully
2025-05-19 14:21:41 [INFO] Config: Verification.SignatureVerification = 0
2025-05-19 14:21:41 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-19 14:21:41 [INFO] Config: Verification.EnablePostExamVerification = 1
2025-05-19 14:21:41 [INFO] Post-exam mode is enabled
2025-05-19 14:21:41 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-19 14:21:41 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-19 14:21:41 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-19 14:21:41 [INFO] Config: Verification.FingerprintMode = save
2025-05-19 14:21:41 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-19 14:21:41 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-19 14:21:41 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-19 14:21:41 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-19 14:21:41 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-19 14:21:41 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-19 14:21:41 [INFO] Config: Paths.dbPath = db
2025-05-19 14:21:52 [INFO] Application exiting: Exit (Code: 0)

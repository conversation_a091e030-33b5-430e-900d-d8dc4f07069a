#Requires AutoHotkey v2.0

; Include error handler
#Include error_handler.ahk

/**
 * PathManager Class
 *
 * A comprehensive path management system for WinCBT-Biometric application.
 * This class centralizes all path-related operations, eliminating hard-coded paths
 * throughout the codebase and providing standardized methods for path retrieval,
 * validation, and creation.
 */
class PathManager {
    ; Static properties for base paths
    static RootDir := A_ScriptDir
    static ConfigFile := A_ScriptDir "\WinCBT-Biometric.ini"

    ; Default paths that can be overridden in config.ini
    static DefaultPaths := Map(
        "Database", "db",
        "Images", "img",
        "Logs", "logs",
        "Temp", "temp",
        "Export", "export",
        "Import", "import",
        "Bin", "bin"
    )

    ; Cached paths to avoid repeated config reads
    static CachedPaths := Map()

    ; Path categories for organization
    static Categories := [
        "Database",
        "Images",
        "Logs",
        "Temp",
        "Export",
        "Import",
        "Bin"
    ]

    ; Database subcategories
    static DatabaseSubcategories := [
        "Config",
        "Candidates",
        "Hardware",
        "Rooms",
        "SeatAssignments",
        "Operators",
        "CandidateImages",
        "Fingerprints",
        "Reports",
        "Backup",
        "Tmp"
    ]

    ; Image subcategories
    static ImageSubcategories := [
        "Default",
        "Icons",
        "Captured"
    ]

    /**
     * Initialize the PathManager
     * Creates necessary directories and ensures WinCBT-Biometric.ini has [Paths] section
     */
    static Initialize() {
        ; Check for old config.ini and migrate if needed
        oldConfigFile := A_ScriptDir "\config.ini"
        if (FileExist(oldConfigFile) && !FileExist(PathManager.ConfigFile)) {
            try {
                ; Copy the old config file to the new name
                FileCopy(oldConfigFile, PathManager.ConfigFile)
                ErrorHandler.LogMessage("INFO", "Migrated settings from config.ini to WinCBT-Biometric.ini")
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to migrate from config.ini: " err.Message)
            }
        }

        ; Ensure WinCBT-Biometric.ini exists and has [Paths] section
        if (!FileExist(PathManager.ConfigFile)) {
            try {
                FileAppend("[Paths]`r`n", PathManager.ConfigFile)
                ErrorHandler.LogMessage("INFO", "Created WinCBT-Biometric.ini with [Paths] section")
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create WinCBT-Biometric.ini: " err.Message)
            }
        } else {
            ; Check if [Paths] section exists, add if not
            fileContent := FileRead(PathManager.ConfigFile)
            if (!InStr(fileContent, "[Paths]")) {
                try {
                    FileAppend("`r`n[Paths]`r`n", PathManager.ConfigFile)
                    ErrorHandler.LogMessage("INFO", "Added [Paths] section to WinCBT-Biometric.ini")
                } catch as err {
                    ErrorHandler.LogMessage("ERROR", "Failed to update WinCBT-Biometric.ini: " err.Message)
                }
            }
        }

        ; Initialize default paths in config if they don't exist
        for category, defaultPath in PathManager.DefaultPaths {
            currentPath := IniRead(PathManager.ConfigFile, "Paths", category "Path", "")
            if (currentPath = "") {
                try {
                    IniWrite(defaultPath, PathManager.ConfigFile, "Paths", category "Path")
                    ErrorHandler.LogMessage("INFO", "Set default " category " path in config.ini: " defaultPath)
                } catch as err {
                    ErrorHandler.LogMessage("ERROR", "Failed to set default " category " path: " err.Message)
                }
            }
        }

        ; Create base directories
        for category in PathManager.Categories {
            path := PathManager.GetPath(category)
            if (!DirExist(path)) {
                try {
                    DirCreate(path)
                    ErrorHandler.LogMessage("INFO", "Created " category " directory: " path)
                } catch as err {
                    ErrorHandler.LogMessage("ERROR", "Failed to create " category " directory: " err.Message)
                }
            }
        }

        ; Create database subdirectories
        dbPath := PathManager.GetDatabasePath()
        for subcat in PathManager.DatabaseSubcategories {
            subcatPath := PathManager.GetDatabaseSubPath(subcat)
            if (!DirExist(subcatPath)) {
                try {
                    DirCreate(subcatPath)
                    ErrorHandler.LogMessage("INFO", "Created database " subcat " directory: " subcatPath)
                } catch as err {
                    ErrorHandler.LogMessage("ERROR", "Failed to create database " subcat " directory: " err.Message)
                }
            }
        }

        ; Create image subdirectories
        imgPath := PathManager.GetImagesPath()
        for subcat in PathManager.ImageSubcategories {
            subcatPath := PathManager.GetImagesSubPath(subcat)
            if (!DirExist(subcatPath)) {
                try {
                    DirCreate(subcatPath)
                    ErrorHandler.LogMessage("INFO", "Created images " subcat " directory: " subcatPath)
                } catch as err {
                    ErrorHandler.LogMessage("ERROR", "Failed to create images " subcat " directory: " err.Message)
                }
            }
        }

        ErrorHandler.LogMessage("INFO", "PathManager initialized successfully")
    }

    /**
     * Get a base path for a category
     * @param {String} category - The category name (Database, Images, etc.)
     * @param {Boolean} validate - Whether to validate the path exists
     * @return {String} The full path
     */
    static GetPath(category, validate := false) {
        ; Check cache first
        if (PathManager.CachedPaths.Has(category)) {
            path := PathManager.CachedPaths[category]
            if (!validate || DirExist(path)) {
                return path
            }
        }

        ; Read from config
        configKey := category "Path"
        relativePath := IniRead(PathManager.ConfigFile, "Paths", configKey, PathManager.DefaultPaths[category])

        ; Convert to absolute path if needed
        if (SubStr(relativePath, 1, 1) != "\" && SubStr(relativePath, 2, 1) != ":") {
            fullPath := PathManager.RootDir "\" relativePath
        } else {
            fullPath := relativePath
        }

        ; Ensure path ends with backslash
        if (SubStr(fullPath, -1) != "\") {
            fullPath := fullPath "\"
        }

        ; Cache the result
        PathManager.CachedPaths[category] := fullPath

        ; Validate if requested
        if (validate && !DirExist(fullPath)) {
            try {
                DirCreate(fullPath)
                ErrorHandler.LogMessage("INFO", "Created missing directory: " fullPath)
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create directory: " err.Message)
                return ""
            }
        }

        return fullPath
    }

    ; ==================== DATABASE PATHS ====================

    /**
     * Get the database base path
     * @param {Boolean} validate - Whether to validate the path exists
     * @return {String} The database path
     */
    static GetDatabasePath(validate := false) {
        return PathManager.GetPath("Database", validate)
    }

    /**
     * Get a database subdirectory path
     * @param {String} subcategory - The subcategory (Config, Candidates, etc.)
     * @param {Boolean} validate - Whether to validate the path exists
     * @return {String} The full path to the subdirectory
     */
    static GetDatabaseSubPath(subcategory, validate := false) {
        dbPath := PathManager.GetDatabasePath(validate)

        ; Map subcategory to directory name
        dirMap := Map(
            "Config", "",
            "Candidates", "",
            "Hardware", "",
            "Rooms", "",
            "SeatAssignments", "",
            "Operators", "",
            "CandidateImages", "img\candidates\",
            "Fingerprints", "fpt\",
            "Reports", "reports\",
            "Backup", "backup\",
            "Tmp", "tmp\"
        )

        subDir := dirMap.Has(subcategory) ? dirMap[subcategory] : subcategory "\"
        fullPath := dbPath subDir

        ; Validate if requested
        if (validate && !DirExist(fullPath) && subDir != "") {
            try {
                DirCreate(fullPath)
                ErrorHandler.LogMessage("INFO", "Created missing database subdirectory: " fullPath)
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create database subdirectory: " err.Message)
                return ""
            }
        }

        return fullPath
    }

    /**
     * Get path to a specific database file
     * @param {String} fileType - The file type (candidates.ini, config.ini, etc.)
     * @param {Boolean} validate - Whether to validate the file exists
     * @return {String} The full path to the file
     */
    static GetDatabaseFilePath(fileType, validate := false) {
        dbPath := PathManager.GetDatabasePath()

        ; Map file type to actual filename
        fileMap := Map(
            "Candidates", "candidates.ini",
            "Config", "config.ini",
            "Hardware", "hardware.ini",
            "Rooms", "rooms.ini",
            "SeatAssignments", "seat_assignments.ini",
            "Operators", "operators.ini",
            "Admin", "admin.ini"
        )

        fileName := fileMap.Has(fileType) ? fileMap[fileType] : fileType
        fullPath := dbPath fileName

        ; Validate if requested
        if (validate && !FileExist(fullPath)) {
            try {
                FileAppend("", fullPath)
                ErrorHandler.LogMessage("INFO", "Created missing database file: " fullPath)
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create database file: " err.Message)
                return ""
            }
        }

        return fullPath
    }

    /**
     * Get path for a candidate's photo
     * @param {String} rollNo - The candidate's roll number
     * @param {Boolean} validate - Whether to validate the directory exists
     * @return {String} The full path to the candidate's photo
     */
    static GetCandidatePhotoPath(rollNo, validate := false) {
        imgPath := PathManager.GetDatabaseSubPath("CandidateImages", validate)
        return imgPath rollNo "_registered_photo.jpg"
    }

    /**
     * Get path for a candidate's signature
     * @param {String} rollNo - The candidate's roll number
     * @param {Boolean} validate - Whether to validate the directory exists
     * @return {String} The full path to the candidate's signature
     */
    static GetCandidateSignaturePath(rollNo, validate := false) {
        imgPath := PathManager.GetDatabaseSubPath("CandidateImages", validate)
        return imgPath rollNo "_signature.jpg"
    }

    /**
     * Get path for a candidate's fingerprint template
     * @param {String} rollNo - The candidate's roll number
     * @param {String} hand - "left" or "right"
     * @param {String} phase - "pre" or "post" for exam phase
     * @param {Boolean} validate - Whether to validate the directory exists
     * @return {String} The full path to the fingerprint template
     */
    static GetFingerprintTemplatePath(rollNo, hand := "left", phase := "pre", validate := false) {
        fptPath := PathManager.GetDatabaseSubPath("Fingerprints", validate)
        return fptPath rollNo "_captured_fingerprint_" hand "_" phase ".fpt"
    }

    /**
     * Get path for a candidate's fingerprint image
     * @param {String} rollNo - The candidate's roll number
     * @param {String} hand - "left" or "right"
     * @param {String} phase - "pre" or "post" for exam phase
     * @param {Boolean} validate - Whether to validate the directory exists
     * @return {String} The full path to the fingerprint image
     */
    static GetFingerprintImagePath(rollNo, hand := "left", phase := "pre", validate := false) {
        fptPath := PathManager.GetDatabaseSubPath("Fingerprints", validate)
        return fptPath rollNo "_captured_fingerprint_" hand "_" phase ".bmp"
    }

    ; ==================== IMAGE PATHS ====================

    /**
     * Get the images base path
     * @param {Boolean} validate - Whether to validate the path exists
     * @return {String} The images path
     */
    static GetImagesPath(validate := false) {
        return PathManager.GetPath("Images", validate)
    }

    /**
     * Get an images subdirectory path
     * @param {String} subcategory - The subcategory (Default, Icons, etc.)
     * @param {Boolean} validate - Whether to validate the path exists
     * @return {String} The full path to the subdirectory
     */
    static GetImagesSubPath(subcategory, validate := false) {
        imgPath := PathManager.GetImagesPath(validate)

        ; Map subcategory to directory name
        dirMap := Map(
            "Default", "",
            "Icons", "icons\",
            "Captured", "captured\"
        )

        subDir := dirMap.Has(subcategory) ? dirMap[subcategory] : subcategory "\"
        fullPath := imgPath subDir

        ; Validate if requested
        if (validate && !DirExist(fullPath) && subDir != "") {
            try {
                DirCreate(fullPath)
                ErrorHandler.LogMessage("INFO", "Created missing images subdirectory: " fullPath)
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create images subdirectory: " err.Message)
                return ""
            }
        }

        return fullPath
    }

    /**
     * Get path to a specific image file
     * @param {String} imageType - The image type (default_photo, gray, etc.)
     * @param {Boolean} validate - Whether to validate the file exists
     * @return {String} The full path to the image
     */
    static GetImageFilePath(imageType, validate := false) {
        ; Special case for CompanyLogo which is in the database images folder
        if (imageType = "CompanyLogo") {
            fullPath := PathManager.GetDatabaseSubPath("CandidateImages", false) "..\company.jpg"

            ; Normalize the path to handle the ".." properly
            Loop Files, fullPath {
                fullPath := A_LoopFileFullPath
                break
            }

            ; If normalization failed, construct the path directly
            if (!fullPath || InStr(fullPath, "..")) {
                fullPath := PathManager.GetDatabasePath() "img\company.jpg"
            }
        } else {
            ; For all other images, use the images folder
            imgPath := PathManager.GetImagesPath()

            ; Map image type to actual filename
            imageMap := Map(
                "DefaultPhoto", "default_photo.png",
                "DefaultFingerprint", "default_fingerprint.png",
                "DefaultSignature", "default_signature.png",
                "Gray", "gray.png",
                "Logo", "logo.png"
            )

            fileName := imageMap.Has(imageType) ? imageMap[imageType] : imageType
            fullPath := imgPath fileName
        }

        ; Validate if requested
        if (validate && !FileExist(fullPath)) {
            ErrorHandler.LogMessage("WARNING", "Image file not found: " fullPath)
            return ""
        }

        return fullPath
    }

    /**
     * Get path for a temporary captured image
     * @param {String} type - The image type (photo, fingerprint, signature)
     * @param {Boolean} validate - Whether to validate the directory exists
     * @return {String} The full path to the temporary image
     */
    static GetCapturedImagePath(type, validate := false) {
        capturedPath := PathManager.GetImagesSubPath("Captured", validate)
        return capturedPath "captured_" type ".png"
    }

    ; ==================== LOG PATHS ====================

    /**
     * Get the logs base path
     * @param {Boolean} validate - Whether to validate the path exists
     * @return {String} The logs path
     */
    static GetLogsPath(validate := false) {
        return PathManager.GetPath("Logs", validate)
    }

    /**
     * Get path to a log file
     * @param {String} logType - The log type (error, info, debug)
     * @param {String} date - The date for the log file (YYYY-MM-DD)
     * @param {Boolean} validate - Whether to validate the directory exists
     * @return {String} The full path to the log file
     */
    static GetLogFilePath(logType := "app", date := "", validate := false) {
        logsPath := PathManager.GetLogsPath(validate)

        ; Use current date if not specified
        if (date = "") {
            date := FormatTime(, "yyyy-MM-dd")
        }

        return logsPath logType "_" date ".log"
    }

    ; ==================== TEMP PATHS ====================

    /**
     * Get the temp base path
     * @param {Boolean} validate - Whether to validate the path exists
     * @return {String} The temp path
     */
    static GetTempPath(validate := false) {
        return PathManager.GetPath("Temp", validate)
    }

    /**
     * Get path for a temporary file
     * @param {String} prefix - The prefix for the temp file
     * @param {String} extension - The file extension
     * @param {Boolean} validate - Whether to validate the directory exists
     * @return {String} The full path to the temporary file
     */
    static GetTempFilePath(prefix, extension := "tmp", validate := false) {
        tempPath := PathManager.GetTempPath(validate)
        timestamp := FormatTime(, "yyyyMMdd_HHmmss")
        return tempPath prefix "_" timestamp "." extension
    }

    ; ==================== EXPORT/IMPORT PATHS ====================

    /**
     * Get the export base path
     * @param {Boolean} validate - Whether to validate the path exists
     * @return {String} The export path
     */
    static GetExportPath(validate := false) {
        return PathManager.GetPath("Export", validate)
    }

    /**
     * Get path for an export file
     * @param {String} type - The export type (candidates, rooms, etc.)
     * @param {String} extension - The file extension (csv, xlsx, etc.)
     * @param {Boolean} validate - Whether to validate the directory exists
     * @return {String} The full path to the export file
     */
    static GetExportFilePath(type, extension := "csv", validate := false) {
        exportPath := PathManager.GetExportPath(validate)
        timestamp := FormatTime(, "yyyyMMdd_HHmmss")
        return exportPath type "_export_" timestamp "." extension
    }

    /**
     * Get the import base path
     * @param {Boolean} validate - Whether to validate the path exists
     * @return {String} The import path
     */
    static GetImportPath(validate := false) {
        return PathManager.GetPath("Import", validate)
    }

    ; ==================== UTILITY METHODS ====================

    /**
     * Check if a path exists and create it if it doesn't
     * @param {String} path - The path to check/create
     * @param {Boolean} isFile - Whether the path is a file (false for directory)
     * @return {Boolean} True if the path exists or was created, false otherwise
     */
    static EnsurePathExists(path, isFile := false) {
        ; Extract directory path if this is a file
        dirPath := isFile ? SubStr(path, 1, InStr(path, "\", , 0) - 1) : path

        ; Check if directory exists
        if (!DirExist(dirPath)) {
            try {
                DirCreate(dirPath)
                ErrorHandler.LogMessage("INFO", "Created directory: " dirPath)
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create directory: " err.Message)
                return false
            }
        }

        ; If this is a file and it doesn't exist, create an empty file
        if (isFile && !FileExist(path)) {
            try {
                FileAppend("", path)
                ErrorHandler.LogMessage("INFO", "Created empty file: " path)
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Failed to create file: " err.Message)
                return false
            }
        }

        return true
    }

    /**
     * Validate that a file exists
     * @param {String} path - The file path to validate
     * @param {Boolean} createIfMissing - Whether to create the file if it doesn't exist
     * @return {Boolean} True if the file exists or was created, false otherwise
     */
    static ValidateFile(path, createIfMissing := false) {
        if (FileExist(path)) {
            return true
        }

        if (createIfMissing) {
            return PathManager.EnsurePathExists(path, true)
        }

        ErrorHandler.LogMessage("WARNING", "File not found: " path)
        return false
    }

    /**
     * Validate that a directory exists
     * @param {String} path - The directory path to validate
     * @param {Boolean} createIfMissing - Whether to create the directory if it doesn't exist
     * @return {Boolean} True if the directory exists or was created, false otherwise
     */
    static ValidateDirectory(path, createIfMissing := false) {
        if (DirExist(path)) {
            return true
        }

        if (createIfMissing) {
            return PathManager.EnsurePathExists(path, false)
        }

        ErrorHandler.LogMessage("WARNING", "Directory not found: " path)
        return false
    }
}
#Requires AutoHotkey v2.0

; Import GUI for WinCBT-Biometric
; This module provides the user interface for importing candidate data

class ImportGUI {
    ; GUI objects
    gui := ""
    importManager := ""

    ; GUI controls
    filePathEdit := ""
    delimiterDropdown := ""
    overwriteCheckbox := ""
    importButton := ""
    cancelButton := ""
    progressBar := ""
    statusText := ""

    ; Constructor
    __New() {
        ; Using static class, no need to create an instance
        global CSVMinimal
        this.importManager := CSVMinimal
    }

    ; Show the import GUI
    Show() {
        ; Create GUI
        this.gui := Gui("+Resize", "Import Candidates")
        this.gui.OnEvent("Close", (*) => this.gui.Destroy())
        this.gui.OnEvent("Size", this.OnResize.Bind(this))

        ; Add controls
        this.gui.SetFont("s10")
        this.gui.Add("Text", "x20 y20 w100", "CSV File:")
        this.filePathEdit := this.gui.Add("Edit", "x130 y20 w300 r1")
        browseButton := this.gui.Add("Button", "x440 y20 w80 h24 +0x1000", "Browse...")
        browseButton.OnEvent("Click", this.OnBrowse.Bind(this))

        this.gui.Add("Text", "x20 y60 w100", "Delimiter:")
        this.delimiterDropdown := this.gui.Add("DropDownList", "x130 y60 w100", ["Comma (,)", "Semicolon (;)", "Tab", "Pipe (|)"])
        this.delimiterDropdown.Choose(1)

        this.overwriteCheckbox := this.gui.Add("CheckBox", "x20 y100 w400", "Overwrite existing candidates")

        this.importButton := this.gui.Add("Button", "x130 y140 w120 h30 +0x1000", "Import")
        this.importButton.OnEvent("Click", this.OnImport.Bind(this))

        this.cancelButton := this.gui.Add("Button", "x260 y140 w120 h30 +0x1000", "Cancel")
        this.cancelButton.OnEvent("Click", (*) => this.gui.Destroy())

        this.gui.Add("Text", "x20 y190 w100", "Progress:")
        this.progressBar := this.gui.Add("Progress", "x130 y190 w390 h20")

        this.statusText := this.gui.Add("Text", "x20 y220 w500")

        ; Add log text area
        this.gui.Add("Text", "x20 y250 w100", "Import Log:")
        this.logEdit := this.gui.Add("Edit", "x20 y270 w500 h200 ReadOnly")

        ; Show GUI
        this.gui.Show("w540 h490")
    }

    ; Handle GUI resize
    OnResize(thisGui, minMax, width, height) {
        if (minMax = -1) ; If minimized
            return

        ; Resize controls
        this.filePathEdit.Move(, , width - 240)
        this.progressBar.Move(, , width - 150)
        this.logEdit.Move(, , width - 40, height - 290)
    }

    ; Handle browse button click
    OnBrowse(*) {
        selectedFile := FileSelect("1", , "Select CSV File", "CSV Files (*.csv)")
        if (selectedFile) {
            this.filePathEdit.Value := selectedFile
        }
    }

    ; Handle import button click
    OnImport(*) {
        ; Get file path
        filePath := this.filePathEdit.Value
        if (!filePath || !FileExist(filePath)) {
            MsgBox("Please select a valid CSV file.", "Import Error", "Icon!")
            return
        }

        ; Get delimiter
        delimiterChoice := this.delimiterDropdown.Value
        delimiter := ","
        switch delimiterChoice {
            case "Semicolon (;)": delimiter := ";"
            case "Tab": delimiter := "`t"
            case "Pipe (|)": delimiter := "|"
        }

        ; Get overwrite option
        overwrite := this.overwriteCheckbox.Value

        ; Disable controls during import
        this.importButton.Enabled := false
        this.cancelButton.Enabled := false
        this.filePathEdit.Enabled := false
        this.delimiterDropdown.Enabled := false
        this.overwriteCheckbox.Enabled := false

        ; Clear log
        this.logEdit.Value := ""

        ; Set options
        options := {
            overwrite: overwrite,
            delimiter: delimiter
        }

        ; Start import in a new thread
        SetTimer(() => this.PerformImport(filePath, options), -1)
    }

    ; Perform the import operation
    PerformImport(filePath, options) {
        ; Update status
        this.statusText.Value := "Importing candidates..."
        this.progressBar.Value := 0

        ; Log start
        this.AppendLog("Starting import from: " filePath)
        this.AppendLog("Options: Delimiter=" options.delimiter ", Overwrite=" options.overwrite)

        ; Perform import
        try {
            results := this.importManager.ImportCandidatesFromCSV(filePath, "", options)

            ; Update progress
            this.progressBar.Value := 100

            ; Log results
            this.AppendLog("Import completed.")
            this.AppendLog("Total rows processed: " results.totalRows)
            this.AppendLog("Candidates imported: " results.imported)
            this.AppendLog("Candidates skipped: " results.skipped)

            ; Log errors
            if (results.errors.Length > 0) {
                this.AppendLog("Errors:")
                for error in results.errors {
                    this.AppendLog("  - " error)
                }
            }

            ; Log validation errors
            if (results.validationErrors.Count > 0) {
                this.AppendLog("Validation errors:")
                for rollNumber, errors in results.validationErrors {
                    this.AppendLog("  Roll Number " rollNumber ":")
                    for error in errors {
                        this.AppendLog("    - " error)
                    }
                }
            }

            ; Update status
            this.statusText.Value := "Import completed: " results.imported " imported, " results.skipped " skipped"

            ; Show completion message
            MsgBox("Import completed.`n`nTotal rows: " results.totalRows "`nImported: " results.imported "`nSkipped: " results.skipped, "Import Complete", "Icon!")
        } catch as err {
            ; Log error
            this.AppendLog("Error during import: " err.Message)

            ; Update status
            this.statusText.Value := "Import failed: " err.Message

            ; Show error message
            MsgBox("Import failed: " err.Message, "Import Error", "Icon!")
        }

        ; Re-enable controls
        this.importButton.Enabled := true
        this.cancelButton.Enabled := true
        this.filePathEdit.Enabled := true
        this.delimiterDropdown.Enabled := true
        this.overwriteCheckbox.Enabled := true
    }

    ; Append text to the log
    AppendLog(text) {
        currentLog := this.logEdit.Value
        if (currentLog != "") {
            currentLog .= "`r`n"
        }
        this.logEdit.Value := currentLog text

        ; Scroll to bottom
        this.logEdit.SendMsg(0x115, 7) ; WM_VSCROLL, SB_BOTTOM
    }
}

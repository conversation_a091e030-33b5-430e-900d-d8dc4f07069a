=== WinCBT-Biometric Error Log ===
Started: 2025-05-22 08:55:06
----------------------------------------
2025-05-22 08:55:06 [INFO] Error handler initialized
2025-05-22 08:55:06 [INFO] Read database path from config: db
2025-05-22 08:55:06 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-22 08:55:06 [INFO] Error handler initialized
2025-05-22 08:55:06 [INFO] Starting WinCBT-Biometric (v1.4.7 Build 20250521)
2025-05-22 08:55:06 [INFO] PathManager initialized successfully
2025-05-22 08:55:06 [INFO] PathManager initialized successfully
2025-05-22 08:55:06 [INFO] Validating required files and directories
2025-05-22 08:55:06 [INFO] Validated directory: Database
2025-05-22 08:55:06 [INFO] Validated directory: Images
2025-05-22 08:55:06 [INFO] Validated directory: Logs
2025-05-22 08:55:06 [INFO] Validated directory: Temporary files
2025-05-22 08:55:06 [INFO] Validated directory: Candidate images
2025-05-22 08:55:06 [INFO] Validated directory: Fingerprint templates
2025-05-22 08:55:06 [INFO] All required files and directories validated successfully
2025-05-22 08:55:06 [INFO] Initializing application components
2025-05-22 08:55:06 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-22 08:55:06 [INFO] Hardware cache loaded with 10 entries
2025-05-22 08:55:06 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-22 08:55:06 [INFO] Room cache loaded with 3 entries
2025-05-22 08:55:06 [INFO] Loading seat assignments from seat ID sections
2025-05-22 08:55:06 [INFO] Loaded 1 seat assignments
2025-05-22 08:55:06 [INFO] Database manager initialized successfully
2025-05-22 08:55:06 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-22 08:55:06 [INFO] Webcam manager initialized
2025-05-22 08:55:06 [INFO] Webcam: Starting webcam...
2025-05-22 08:55:06 [INFO] Webcam: Webcam active
2025-05-22 08:55:06 [INFO] Webcam started successfully
2025-05-22 08:55:06 [INFO] Config: Verification.SignatureVerification = 0
2025-05-22 08:55:06 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-22 08:55:07 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-22 08:55:07 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-22 08:55:07 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-22 08:55:07 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-22 08:55:07 [INFO] Config: Verification.FingerprintMode = save
2025-05-22 08:55:07 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-22 08:55:07 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-22 08:55:07 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-22 08:55:07 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-22 08:55:07 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-22 08:55:07 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-22 08:55:07 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-22 08:55:07 [INFO] Added webcam feed control with default photo
2025-05-22 08:55:07 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-22 08:55:09 [INFO] Application exiting: Exit (Code: 0)
2025-05-22 08:55:21 [INFO] Error handler initialized
2025-05-22 08:55:21 [INFO] Read database path from config: db
2025-05-22 08:55:21 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-22 08:55:21 [INFO] Error handler initialized
2025-05-22 08:55:21 [INFO] Starting WinCBT-Biometric (v1.4.7 Build 20250521)
2025-05-22 08:55:21 [INFO] PathManager initialized successfully
2025-05-22 08:55:21 [INFO] PathManager initialized successfully
2025-05-22 08:55:21 [INFO] Validating required files and directories
2025-05-22 08:55:21 [INFO] Validated directory: Database
2025-05-22 08:55:21 [INFO] Validated directory: Images
2025-05-22 08:55:21 [INFO] Validated directory: Logs
2025-05-22 08:55:21 [INFO] Validated directory: Temporary files
2025-05-22 08:55:21 [INFO] Validated directory: Candidate images
2025-05-22 08:55:21 [INFO] Validated directory: Fingerprint templates
2025-05-22 08:55:22 [INFO] All required files and directories validated successfully
2025-05-22 08:55:22 [INFO] Initializing application components
2025-05-22 08:55:22 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-22 08:55:22 [INFO] Hardware cache loaded with 10 entries
2025-05-22 08:55:22 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-22 08:55:22 [INFO] Room cache loaded with 3 entries
2025-05-22 08:55:22 [INFO] Loading seat assignments from seat ID sections
2025-05-22 08:55:22 [INFO] Loaded 1 seat assignments
2025-05-22 08:55:22 [INFO] Database manager initialized successfully
2025-05-22 08:55:22 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-22 08:55:22 [INFO] Webcam manager initialized
2025-05-22 08:55:22 [INFO] Webcam: Starting webcam...
2025-05-22 08:55:22 [INFO] Webcam: Webcam active
2025-05-22 08:55:22 [INFO] Webcam started successfully
2025-05-22 08:55:22 [INFO] Config: Verification.SignatureVerification = 0
2025-05-22 08:55:22 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-22 08:55:22 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-22 08:55:22 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-22 08:55:22 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-22 08:55:22 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-22 08:55:22 [INFO] Config: Verification.FingerprintMode = save
2025-05-22 08:55:22 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-22 08:55:22 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-22 08:55:22 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-22 08:55:22 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-22 08:55:22 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-22 08:55:22 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-22 08:55:22 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-22 08:55:22 [INFO] Added webcam feed control with default photo
2025-05-22 08:55:22 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-22 08:55:31 [INFO] Application exiting: Exit (Code: 0)
2025-05-22 08:55:41 [INFO] Error handler initialized
2025-05-22 08:55:41 [INFO] Read database path from config: db
2025-05-22 08:55:41 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-22 08:55:41 [INFO] Error handler initialized
2025-05-22 08:55:41 [INFO] Starting WinCBT-Biometric (v1.4.7 Build 20250521)
2025-05-22 08:55:41 [INFO] PathManager initialized successfully
2025-05-22 08:55:41 [INFO] PathManager initialized successfully
2025-05-22 08:55:41 [INFO] Validating required files and directories
2025-05-22 08:55:41 [INFO] Validated directory: Database
2025-05-22 08:55:41 [INFO] Validated directory: Images
2025-05-22 08:55:41 [INFO] Validated directory: Logs
2025-05-22 08:55:41 [INFO] Validated directory: Temporary files
2025-05-22 08:55:41 [INFO] Validated directory: Candidate images
2025-05-22 08:55:41 [INFO] Validated directory: Fingerprint templates
2025-05-22 08:55:41 [INFO] All required files and directories validated successfully
2025-05-22 08:55:41 [INFO] Initializing application components
2025-05-22 08:55:41 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-22 08:55:41 [INFO] Hardware cache loaded with 10 entries
2025-05-22 08:55:41 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-22 08:55:41 [INFO] Room cache loaded with 3 entries
2025-05-22 08:55:41 [INFO] Loading seat assignments from seat ID sections
2025-05-22 08:55:41 [INFO] Loaded 1 seat assignments
2025-05-22 08:55:41 [INFO] Database manager initialized successfully
2025-05-22 08:55:41 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-22 08:55:41 [INFO] Webcam manager initialized
2025-05-22 08:55:41 [INFO] Webcam: Starting webcam...
2025-05-22 08:55:41 [INFO] Webcam: No webcam found
2025-05-22 08:55:41 [WARNING] Failed to start webcam
2025-05-22 08:55:41 [INFO] Config: Verification.SignatureVerification = 0
2025-05-22 08:55:41 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-22 08:55:41 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-22 08:55:41 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-22 08:55:41 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-22 08:55:41 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-22 08:55:41 [INFO] Config: Verification.FingerprintMode = save
2025-05-22 08:55:41 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-22 08:55:41 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-22 08:55:41 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-22 08:55:41 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-22 08:55:41 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-22 08:55:41 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-22 08:55:41 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-22 08:55:41 [INFO] Added webcam feed control with default photo
2025-05-22 08:55:41 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-22 08:55:44 [INFO] Application exiting: Exit (Code: 0)
2025-05-22 08:56:04 [INFO] Error handler initialized
2025-05-22 08:56:04 [INFO] Read database path from config: db
2025-05-22 08:56:04 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-22 08:56:04 [INFO] Error handler initialized
2025-05-22 08:56:04 [INFO] Starting WinCBT-Biometric (v1.4.7 Build 20250521)
2025-05-22 08:56:04 [INFO] PathManager initialized successfully
2025-05-22 08:56:04 [INFO] PathManager initialized successfully
2025-05-22 08:56:04 [INFO] Validating required files and directories
2025-05-22 08:56:05 [INFO] Validated directory: Database
2025-05-22 08:56:05 [INFO] Validated directory: Images
2025-05-22 08:56:05 [INFO] Validated directory: Logs
2025-05-22 08:56:05 [INFO] Validated directory: Temporary files
2025-05-22 08:56:05 [INFO] Validated directory: Candidate images
2025-05-22 08:56:05 [INFO] Validated directory: Fingerprint templates
2025-05-22 08:56:05 [INFO] All required files and directories validated successfully
2025-05-22 08:56:05 [INFO] Initializing application components
2025-05-22 08:56:05 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-22 08:56:05 [INFO] Hardware cache loaded with 10 entries
2025-05-22 08:56:05 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-22 08:56:05 [INFO] Room cache loaded with 3 entries
2025-05-22 08:56:05 [INFO] Loading seat assignments from seat ID sections
2025-05-22 08:56:05 [INFO] Loaded 1 seat assignments
2025-05-22 08:56:05 [INFO] Database manager initialized successfully
2025-05-22 08:56:05 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-22 08:56:05 [INFO] Webcam manager initialized
2025-05-22 08:56:05 [INFO] Webcam: Starting webcam...
2025-05-22 08:56:07 [INFO] Webcam: Webcam active
2025-05-22 08:56:07 [INFO] Webcam started successfully
2025-05-22 08:56:07 [INFO] Config: Verification.SignatureVerification = 0
2025-05-22 08:56:07 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-22 08:56:07 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-22 08:56:07 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-22 08:56:07 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-22 08:56:07 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-22 08:56:07 [INFO] Config: Verification.FingerprintMode = save
2025-05-22 08:56:07 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-22 08:56:07 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-22 08:56:07 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-22 08:56:07 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-22 08:56:07 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-22 08:56:07 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-22 08:56:07 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-22 08:56:07 [INFO] Added webcam feed control with default photo
2025-05-22 08:56:07 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-22 08:56:10 [INFO] Application exiting: Exit (Code: 0)
2025-05-22 08:56:26 [INFO] Error handler initialized
2025-05-22 08:56:26 [INFO] Read database path from config: db
2025-05-22 08:56:26 [INFO] Using database path: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\
2025-05-22 08:56:26 [INFO] Error handler initialized
2025-05-22 08:56:26 [INFO] Starting WinCBT-Biometric (v1.4.7 Build 20250521)
2025-05-22 08:56:26 [INFO] PathManager initialized successfully
2025-05-22 08:56:26 [INFO] PathManager initialized successfully
2025-05-22 08:56:26 [INFO] Validating required files and directories
2025-05-22 08:56:26 [INFO] Validated directory: Database
2025-05-22 08:56:26 [INFO] Validated directory: Images
2025-05-22 08:56:26 [INFO] Validated directory: Logs
2025-05-22 08:56:26 [INFO] Validated directory: Temporary files
2025-05-22 08:56:26 [INFO] Validated directory: Candidate images
2025-05-22 08:56:26 [INFO] Validated directory: Fingerprint templates
2025-05-22 08:56:26 [INFO] All required files and directories validated successfully
2025-05-22 08:56:26 [INFO] Initializing application components
2025-05-22 08:56:26 [INFO] Loading hardware cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-22 08:56:26 [INFO] Hardware cache loaded with 10 entries
2025-05-22 08:56:26 [INFO] Loading room cache from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-22 08:56:26 [INFO] Room cache loaded with 3 entries
2025-05-22 08:56:26 [INFO] Loading seat assignments from seat ID sections
2025-05-22 08:56:26 [INFO] Loaded 1 seat assignments
2025-05-22 08:56:26 [INFO] Database manager initialized successfully
2025-05-22 08:56:26 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-22 08:56:26 [INFO] Webcam manager initialized
2025-05-22 08:56:26 [INFO] Webcam: Starting webcam...
2025-05-22 08:56:27 [INFO] Webcam: Webcam active
2025-05-22 08:56:27 [INFO] Webcam started successfully
2025-05-22 08:56:27 [INFO] Config: Verification.SignatureVerification = 0
2025-05-22 08:56:27 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-22 08:56:27 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-22 08:56:27 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-22 08:56:27 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-22 08:56:27 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-22 08:56:27 [INFO] Config: Verification.FingerprintMode = save
2025-05-22 08:56:27 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-22 08:56:27 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-22 08:56:27 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-22 08:56:27 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-22 08:56:27 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-22 08:56:27 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-22 08:56:27 [INFO] Loaded company logo from: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-22 08:56:27 [INFO] Added webcam feed control with default photo
2025-05-22 08:56:27 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-22 08:56:33 [INFO] Application exiting: Exit (Code: 0)
2025-05-22 11:05:33 [INFO] Error handler initialized
2025-05-22 11:05:33 [INFO] Read database path from config: db
2025-05-22 11:05:33 [INFO] Using database path: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\
2025-05-22 11:05:33 [INFO] Error handler initialized
2025-05-22 11:05:33 [INFO] Starting WinCBT-Biometric (v1.4.7 Build 20250521)
2025-05-22 11:05:33 [INFO] PathManager initialized successfully
2025-05-22 11:05:33 [INFO] PathManager initialized successfully
2025-05-22 11:05:33 [INFO] Validating required files and directories
2025-05-22 11:05:33 [INFO] Validated directory: Database
2025-05-22 11:05:33 [INFO] Validated directory: Images
2025-05-22 11:05:33 [INFO] Validated directory: Logs
2025-05-22 11:05:33 [INFO] Validated directory: Temporary files
2025-05-22 11:05:33 [INFO] Validated directory: Candidate images
2025-05-22 11:05:33 [INFO] Validated directory: Fingerprint templates
2025-05-22 11:05:33 [INFO] All required files and directories validated successfully
2025-05-22 11:05:33 [INFO] Initializing application components
2025-05-22 11:05:33 [INFO] Loading hardware cache from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-22 11:05:33 [INFO] Hardware cache loaded with 10 entries
2025-05-22 11:05:33 [INFO] Loading room cache from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-22 11:05:33 [INFO] Room cache loaded with 3 entries
2025-05-22 11:05:33 [INFO] Loading seat assignments from seat ID sections
2025-05-22 11:05:33 [INFO] Loaded 1 seat assignments
2025-05-22 11:05:33 [INFO] Database manager initialized successfully
2025-05-22 11:05:33 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-22 11:05:33 [INFO] Webcam manager initialized
2025-05-22 11:05:33 [INFO] Webcam: Starting webcam...
2025-05-22 11:05:33 [INFO] Webcam: No webcam found
2025-05-22 11:05:33 [WARNING] Failed to start webcam
2025-05-22 11:05:33 [INFO] Config: Verification.SignatureVerification = 0
2025-05-22 11:05:33 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-22 11:05:33 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-22 11:05:33 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-22 11:05:33 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-22 11:05:33 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-22 11:05:33 [INFO] Config: Verification.FingerprintMode = save
2025-05-22 11:05:33 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-22 11:05:33 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-22 11:05:33 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-22 11:05:33 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-22 11:05:33 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-22 11:05:33 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-22 11:05:33 [INFO] Loaded company logo from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-22 11:05:33 [INFO] Added webcam feed control with default photo
2025-05-22 11:05:34 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-22 11:05:39 [INFO] Webcam: No webcam found
2025-05-22 11:05:40 [INFO] Found seat assignment in cache for 9351: F1-R1-S8
2025-05-22 11:05:41 [INFO] Application exiting: Exit (Code: 0)
2025-05-22 11:09:17 [INFO] Error handler initialized
2025-05-22 11:09:17 [INFO] Read database path from config: db
2025-05-22 11:09:17 [INFO] Using database path: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\
2025-05-22 11:09:17 [INFO] Error handler initialized
2025-05-22 11:09:17 [INFO] Starting WinCBT-Biometric (v1.4.7 Build 20250521)
2025-05-22 11:09:17 [INFO] PathManager initialized successfully
2025-05-22 11:09:17 [INFO] PathManager initialized successfully
2025-05-22 11:09:17 [INFO] Validating required files and directories
2025-05-22 11:09:17 [INFO] Validated directory: Database
2025-05-22 11:09:17 [INFO] Validated directory: Images
2025-05-22 11:09:17 [INFO] Validated directory: Logs
2025-05-22 11:09:17 [INFO] Validated directory: Temporary files
2025-05-22 11:09:17 [INFO] Validated directory: Candidate images
2025-05-22 11:09:17 [INFO] Validated directory: Fingerprint templates
2025-05-22 11:09:17 [INFO] All required files and directories validated successfully
2025-05-22 11:09:17 [INFO] Initializing application components
2025-05-22 11:09:17 [INFO] Loading hardware cache from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\hardware.ini
2025-05-22 11:09:17 [INFO] Hardware cache loaded with 10 entries
2025-05-22 11:09:17 [INFO] Loading room cache from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\rooms.ini
2025-05-22 11:09:17 [INFO] Room cache loaded with 3 entries
2025-05-22 11:09:17 [INFO] Loading seat assignments from seat ID sections
2025-05-22 11:09:17 [INFO] Loaded 1 seat assignments
2025-05-22 11:09:17 [INFO] Database manager initialized successfully
2025-05-22 11:09:17 [INFO] SecuGen fingerprint manager initialized successfully
2025-05-22 11:09:17 [INFO] Webcam manager initialized
2025-05-22 11:09:17 [INFO] Webcam: Starting webcam...
2025-05-22 11:09:17 [INFO] Webcam: No webcam found
2025-05-22 11:09:17 [WARNING] Failed to start webcam
2025-05-22 11:09:17 [INFO] Config: Verification.SignatureVerification = 0
2025-05-22 11:09:17 [INFO] Config: Verification.RightThumbprintVerification = 0
2025-05-22 11:09:17 [INFO] Config: Verification.EnablePostExamVerification = 0
2025-05-22 11:09:17 [INFO] Config: Verification.PhotoVerificationMode = Both
2025-05-22 11:09:17 [INFO] Config: Verification.SignatureVerificationMode = Both
2025-05-22 11:09:17 [INFO] Config: Verification.FingerprintVerificationMode = Both
2025-05-22 11:09:17 [INFO] Config: Verification.FingerprintMode = save
2025-05-22 11:09:17 [INFO] Config: Verification.PhotoConfidenceThreshold = 85
2025-05-22 11:09:17 [INFO] Config: Verification.SignatureConfidenceThreshold = 85
2025-05-22 11:09:17 [INFO] Config: Verification.FingerprintConfidenceThreshold = 85
2025-05-22 11:09:17 [INFO] Config: Verification.FingerprintCaptureThreshold = 70
2025-05-22 11:09:17 [INFO] Config: Verification.FingerprintAutoSaveThreshold = 80
2025-05-22 11:09:17 [INFO] Config: Verification.AllowMissingFingerprint = 1
2025-05-22 11:09:17 [INFO] Loaded company logo from: D:\MY\Sync\WORK\CBT\WinCBT-Biometric\db\img\company.jpg
2025-05-22 11:09:18 [INFO] Added webcam feed control with default photo
2025-05-22 11:09:18 [INFO] Initial setup: Signature verification disabled - status set to Disabled
2025-05-22 11:09:32 [INFO] Application exiting: Exit (Code: 0)

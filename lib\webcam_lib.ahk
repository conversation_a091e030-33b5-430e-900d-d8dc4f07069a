#Requires AutoHotkey v2.0

; ===================================================================
; WinCBT-Biometric Webcam Library
; Provides functions for webcam feed and image capture
;
; Used by: WinCBT-Biometric
;
; This file is specific to WinCBT-Biometric and is not shared with
; WinCBT-Admin.
; ===================================================================

class WebcamManager {
    ; Properties
    hModule := 0
    capHwnd := 0
    isWebcamActive := false
    WDT := 640
    HGT := 480
    FPS := 15
    webcamParent := 0
    webcamControl := 0
    captureFile := ""
    savePath := ""
    statusCallback := 0
    refreshTimer := 0
    dismissDialogsTimer := 0
    cameraName := "HD Pro Webcam C920"  ; Default camera name

    ; ; __New(parentGui, webcamControl, width := 640, height := 480, fps := 15)
    ; ; Constructor for the WebcamManager class.
    ; ; Initializes properties, loads the avicap32.dll, sets up paths, and registers cleanup.
    ; ; @param parentGui: The parent Gui object that will host the webcam feed.
    ; ; @param webcamControl: The Picture control within the parentGui to display the feed.
    ; ; @param width: Desired width of the webcam feed (default 640).
    ; ; @param height: Desired height of the webcam feed (default 480).
    ; ; @param fps: Desired frames per second for the preview (default 15).
    __New(parentGui, webcamControl, width := 640, height := 480, fps := 15) {
        ; Load the Video for Windows library
        this.hModule := DllCall("LoadLibrary", "Str", "avicap32.dll")

        ; Store parameters
        this.webcamParent := parentGui
        this.webcamControl := webcamControl
        this.WDT := width
        this.HGT := height
        this.FPS := fps

        ; Set up path for captured images
        this.captureFile := A_Temp "\webcam_capture.jpg"
        this.savePath := A_ScriptDir "\img"

        ; Make sure the capture directory exists
        if !DirExist(this.savePath)
            DirCreate(this.savePath)

        ; Register cleanup on exit
        OnExit(ObjBindMethod(this, "CleanUp"))
    }

    ; ; __Delete()
    ; ; Destructor for the WebcamManager class. Calls CleanUp to release resources.
    __Delete() {
        this.CleanUp()
    }

    ; ; SetStatusCallback(callback)
    ; ; Sets a callback function to receive status updates from the WebcamManager.
    ; ; @param callback: A function object that accepts a single string argument (the status message).
    SetStatusCallback(callback) {
        this.statusCallback := callback
    }

    ; ; ReadCameraSettings(configFile)
    ; ; Reads camera settings from the config file.
    ; ; @param configFile: The path to the config file.
    ReadCameraSettings(configFile) {
        if (!FileExist(configFile))
            return

        try {
            ; First try to read from the new Camera section
            cameraName := IniRead(configFile, "Camera", "CameraName", "NOT_FOUND")

            ; If camera name found in Camera section
            if (cameraName != "NOT_FOUND") {
                ; Remove quotes if present
                if (SubStr(cameraName, 1, 1) == "`"" && SubStr(cameraName, -1) == "`"") {
                    cameraName := SubStr(cameraName, 2, StrLen(cameraName) - 2)
                }
                this.cameraName := cameraName
                OutputDebug("Read camera name from Camera section: " . this.cameraName)
            } else {
                ; Try the old BiometricDevices section for backward compatibility
                cameraName := IniRead(configFile, "BiometricDevices", "Camera", "")
                if (cameraName != "") {
                    this.cameraName := cameraName
                    OutputDebug("Read camera name from BiometricDevices section: " . this.cameraName)
                }
            }
        } catch Error as e {
            OutputDebug("Error reading camera settings: " . e.Message)
        }
    }

    ; ; StartWebcam()
    ; ; Initializes and starts the webcam preview feed within the designated control.
    ; ; Creates the capture window, connects to the driver, and sets preview parameters.
    ; ; Starts a timer to periodically refresh the feed.
    ; ; @return: True if the webcam started successfully, False otherwise.
    StartWebcam() {
        ; Don't initialize if already active
        if (this.isWebcamActive)
            return false

        try {
            ; Update status
            this.UpdateStatus("Starting webcam...")

            ; Read camera settings from WinCBT-Biometric.ini first to ensure we have the latest settings
            configFile := A_ScriptDir "\WinCBT-Biometric.ini"
            if (FileExist(configFile)) {
                OutputDebug("Reading camera settings from WinCBT-Biometric.ini")
                this.ReadCameraSettings(configFile)
            } else if (FileExist(A_ScriptDir "\config.ini")) {
                OutputDebug("Reading camera settings from config.ini")
                this.ReadCameraSettings(A_ScriptDir "\config.ini")
            }

            ; Get position for the webcam control
            this.webcamControl.GetPos(&ctrlX, &ctrlY, &ctrlW, &ctrlH)

            ; Create window for webcam
            this.capHwnd := this.Cap_CreateCaptureWindow(this.webcamParent.Hwnd, ctrlX, ctrlY, ctrlW, ctrlH)

            ; Define message constants
            WM_CAP := 0x400
            WM_CAP_DRIVER_CONNECT := WM_CAP + 10
            WM_CAP_SET_PREVIEW := WM_CAP + 50
            WM_CAP_SET_PREVIEWRATE := WM_CAP + 52
            WM_CAP_SET_SCALE := WM_CAP + 53

            ; Always connect to camera index 0
            
            result := SendMessage(WM_CAP_DRIVER_CONNECT, 0, 0, , "ahk_id " this.capHwnd)

            if (result) {
                ; Successfully connected to the camera

                ; Set the preview scale
                SendMessage(WM_CAP_SET_SCALE, 1, 0, , "ahk_id " this.capHwnd)

                ; Set the preview rate in milliseconds
                fps_ms := Round((1/this.FPS)*1000)
                SendMessage(WM_CAP_SET_PREVIEWRATE, fps_ms, 0, , "ahk_id " this.capHwnd)

                ; Start previewing the image from the camera
                SendMessage(WM_CAP_SET_PREVIEW, 1, 0, , "ahk_id " this.capHwnd)

                this.isWebcamActive := true
                this.UpdateStatus("Webcam active")

                ; Set a timer to periodically refresh the webcam preview
                SetTimer(ObjBindMethod(this, "RefreshWebcam"), 5000, this.refreshTimer)  ; Refresh every 5 seconds

                ; Set up a timer to automatically dismiss any dialogs that appear
                SetTimer(ObjBindMethod(this, "DismissDialogs"), 100, this.dismissDialogsTimer)
                OutputDebug("Started dialog dismissal timer")

                return true
            } else {
                this.UpdateStatus("No webcam found")
                return false
            }
        } catch as err {
            this.UpdateStatus("Webcam initialization failed: " err.Message)
            return false
        }
    }

    ; ; StopWebcam()
    ; ; Stops the webcam preview feed and disconnects from the driver.
    ; ; Destroys the capture window and clears the refresh timer.
    StopWebcam() {
        OutputDebug("StopWebcam called")

        ; If webcam is not active, just return
        if (!this.isWebcamActive) {
            OutputDebug("Webcam is not active, nothing to stop")
            return
        }

        ; Clear the refresh timer first
        if (this.refreshTimer) {
            try {
                OutputDebug("Stopping refresh timer")
                SetTimer(this.refreshTimer, 0)
                this.refreshTimer := 0
            } catch as err {
                OutputDebug("Error stopping refresh timer: " err.Message)
            }
        }

        ; Clear the dismiss dialogs timer
        if (this.dismissDialogsTimer) {
            try {
                OutputDebug("Stopping dismiss dialogs timer")
                SetTimer(this.dismissDialogsTimer, 0)
                this.dismissDialogsTimer := 0
            } catch as err {
                OutputDebug("Error stopping dismiss dialogs timer: " err.Message)
            }
        }

        ; Disconnect from webcam
        if (this.capHwnd) {
            try {
                ; Check if the window still exists
                if (DllCall("IsWindow", "Ptr", this.capHwnd)) {
                    OutputDebug("Disconnecting from webcam driver")
                    WM_CAP := 0x400
                    WM_CAP_DRIVER_DISCONNECT := WM_CAP + 11

                    ; Try to disconnect from the driver
                    try {
                        SendMessage(WM_CAP_DRIVER_DISCONNECT, 0, 0, , "ahk_id " this.capHwnd)
                        OutputDebug("Successfully disconnected from webcam driver")
                    } catch as err {
                        OutputDebug("Error disconnecting from webcam driver: " err.Message)
                    }

                    ; Try to destroy the window
                    try {
                        OutputDebug("Destroying webcam window")
                        DllCall("DestroyWindow", "Ptr", this.capHwnd)
                        OutputDebug("Successfully destroyed webcam window")
                    } catch as err {
                        OutputDebug("Error destroying webcam window: " err.Message)
                    }
                } else {
                    OutputDebug("Webcam window no longer exists, skipping disconnect")
                }
            } catch as err {
                OutputDebug("Error checking webcam window: " err.Message)
            }

            ; Reset the handle regardless of success
            this.capHwnd := 0
        } else {
            OutputDebug("No webcam handle, nothing to disconnect")
        }

        ; Mark webcam as inactive
        this.isWebcamActive := false
        this.UpdateStatus("Webcam stopped")
        OutputDebug("Webcam successfully stopped")
    }

    ; ; CaptureFrame(filename)
    ; ; Captures a single frame from the webcam and saves it to the specified file using FFmpeg.
    ; ; Note: This method relies on an external ffmpeg.exe located in A_ScriptDir "\bin\".
    ; ; It temporarily stops the preview feed during capture.
    ; ; @param filename: The full path and filename where the captured image should be saved (e.g., ".jpg").
    ; ; @return: The filename if capture was successful, False otherwise.
    CaptureFrame(filename) {
        if (filename == "") {
             this.UpdateStatus("Cannot capture: No filename provided")
             return false
        }

        try {
            ; Delete any existing file with the same name
            if (FileExist(filename)) {
                try FileDelete(filename)
                catch {
                    this.UpdateStatus("Could not delete existing file: " filename)
                }
            }

            ; Create command to run FFmpeg directly
            ffmpegPath := A_ScriptDir "\bin\ffmpeg.exe"
            if (!FileExist(ffmpegPath)) {
                 this.UpdateStatus("FFmpeg executable not found at: " ffmpegPath)
                 return false
            }

            ; Read the latest camera settings from WinCBT-Biometric.ini
            configFile := A_ScriptDir "\WinCBT-Biometric.ini"

            OutputDebug("CaptureFrame: Using camera name: " . this.cameraName)

            ; Create FFmpeg command with the camera name
            cmd := '"' . ffmpegPath . '" -y -f dshow -i video="' . this.cameraName . '" -frames:v 1 -q:v 2 "' . filename . '"'

            ; Run FFmpeg to capture the image and get its exit code
            this.UpdateStatus("Capturing image via FFmpeg from camera: " this.cameraName)
            exitCode := RunWait(cmd, , "Hide")

            ; Check the exit code returned by RunWait
            if (exitCode != 0) {
                this.UpdateStatus("FFmpeg failed with Exit Code: " exitCode)

                ; Try to detect available cameras using FFmpeg
                OutputDebug("Trying to detect available cameras")
                cameras := DetectCameras()

                if (cameras.Length > 0) {
                    ; Try each detected camera
                    for i, camera in cameras {
                        if (camera = this.cameraName)
                            continue  ; Skip the one we already tried

                        this.UpdateStatus("Trying with camera: " camera)
                        OutputDebug("Trying with camera: " camera)

                        fallbackCmd := '"' . ffmpegPath . '" -y -f dshow -i video="' . camera . '" -frames:v 1 -q:v 2 "' . filename . '"'
                        exitCode := RunWait(fallbackCmd, , "Hide")

                        if (exitCode = 0) {
                            ; This camera worked, save it to config
                            try {
                                IniWrite(camera, A_ScriptDir "\WinCBT-Biometric.ini", "Camera", "CameraName")
                                OutputDebug("Saved working camera to config: " camera)
                                this.cameraName := camera
                            } catch as err {
                                OutputDebug("Error saving camera: " err.Message)
                            }
                            break
                        }
                    }
                } else {
                    ; If no cameras detected, try with default camera as last resort
                    if (this.cameraName != "HD Pro Webcam C920") {
                        this.UpdateStatus("Trying with default camera as fallback...")
                        defaultCmd := '"' . ffmpegPath . '" -y -f dshow -i video="HD Pro Webcam C920" -frames:v 1 -q:v 2 "' . filename . '"'
                        exitCode := RunWait(defaultCmd, , "Hide")
                    }
                }

                ; If all attempts failed
                if (exitCode != 0) {
                    this.UpdateStatus("All camera attempts failed")
                    return false
                }
            }

            ; Check if the capture was successful by verifying file existence
            if (FileExist(filename)) {
                this.UpdateStatus("Image captured successfully: " filename)
                return filename
            } else {
                this.UpdateStatus("Image capture failed (File not found after FFmpeg run)")
                return false
            }
        } catch as err {
            this.UpdateStatus("Error during capture: " err.Message)
            return false
        }
    }

    ; ; RestartWebcam()
    ; ; Restarts the webcam preview after it might have been stopped (e.g., after capture).
    ; ; Re-creates the capture window if necessary, connects to the driver, and resumes preview.
    RestartWebcam() {
        OutputDebug("RestartWebcam called")

        ; First create a new capture window if needed
        if (!this.capHwnd || !DllCall("IsWindow", "Ptr", this.capHwnd)) {
            ; Get position information for the placeholder
            this.webcamControl.GetPos(&ctrlX, &ctrlY, &ctrlW, &ctrlH)
            OutputDebug("Creating new capture window at: " ctrlX "," ctrlY " size: " ctrlW "x" ctrlH)

            ; Create a new capture window
            newHwnd := this.Cap_CreateCaptureWindow(this.webcamParent.Hwnd, ctrlX, ctrlY, ctrlW, ctrlH)

            ; Only destroy old window after creating new one
            if (this.capHwnd)
                DllCall("DestroyWindow", "Ptr", this.capHwnd)

            this.capHwnd := newHwnd
        }

        ; Define message constants
        WM_CAP := 0x400
        WM_CAP_DRIVER_CONNECT := WM_CAP + 10
        WM_CAP_SET_PREVIEW := WM_CAP + 50
        WM_CAP_SET_PREVIEWRATE := WM_CAP + 52
        WM_CAP_SET_SCALE := WM_CAP + 53

        ; Read the latest camera settings from WinCBT-Biometric.ini
        configFile := A_ScriptDir "\WinCBT-Biometric.ini"
        if (FileExist(configFile)) {
            OutputDebug("Reading camera settings from WinCBT-Biometric.ini")
            this.ReadCameraSettings(configFile)
        } else if (FileExist(A_ScriptDir "\config.ini")) {
            OutputDebug("Reading camera settings from config.ini")
            this.ReadCameraSettings(A_ScriptDir "\config.ini")
        }

        ; Always connect to camera index 0
        OutputDebug("RestartWebcam: Connecting to camera at index 0")
        result := SendMessage(WM_CAP_DRIVER_CONNECT, 0, 0, , "ahk_id " this.capHwnd)

        if (result) {
            ; Successfully connected to the camera

            ; Set the preview scale
            SendMessage(WM_CAP_SET_SCALE, 1, 0, , "ahk_id " this.capHwnd)

            ; Set the preview rate in milliseconds
            fps_ms := Round((1/this.FPS)*1000)
            SendMessage(WM_CAP_SET_PREVIEWRATE, fps_ms, 0, , "ahk_id " this.capHwnd)

            ; Start previewing the image from the camera
            SendMessage(WM_CAP_SET_PREVIEW, 1, 0, , "ahk_id " this.capHwnd)

            this.isWebcamActive := true
            this.UpdateStatus("Webcam active")

            ; Set a timer to periodically refresh the webcam preview
            SetTimer(ObjBindMethod(this, "RefreshWebcam"), 5000, this.refreshTimer)

            ; Set up a timer to automatically dismiss any dialogs that appear
            SetTimer(ObjBindMethod(this, "DismissDialogs"), 100, this.dismissDialogsTimer)
            OutputDebug("Started dialog dismissal timer in RestartWebcam")

            ; Get correct position for webcam control
            this.webcamControl.GetPos(&ctrlX, &ctrlY, &ctrlW, &ctrlH)

            ; Move window to correct position
            DllCall("SetWindowPos", "Ptr", this.capHwnd, "Ptr", 0
                , "Int", ctrlX, "Int", ctrlY  ; Correct x,y position
                , "Int", ctrlW, "Int", ctrlH  ; Width and height
                , "UInt", 0x0010)  ; SWP_NOACTIVATE

            OutputDebug("Webcam restarted successfully")
            return true
        } else {
            this.UpdateStatus("No webcam found")
            this.isWebcamActive := false
            OutputDebug("Failed to restart webcam - no camera found")
            return false
        }
    }

    ; ; RefreshWebcam()
    ; ; Internal method called by a timer to periodically refresh the webcam preview.
    ; ; Helps prevent the feed from freezing or stopping unexpectedly in some cases
    ; ; by briefly toggling the preview off and on.
    RefreshWebcam() {
        ; Only refresh if webcam is supposed to be active
        if (!this.isWebcamActive || !this.capHwnd)
            return

        ; Check if the window is still valid
        if (!DllCall("IsWindow", "Ptr", this.capHwnd))
            return

        ; Refresh the preview
        WM_CAP := 0x400
        WM_CAP_SET_PREVIEW := WM_CAP + 50

        ; Toggle preview off and on again to refresh it
        SendMessage(WM_CAP_SET_PREVIEW, 0, 0, , "ahk_id " this.capHwnd)
        Sleep(50)
        SendMessage(WM_CAP_SET_PREVIEW, 1, 0, , "ahk_id " this.capHwnd)
    }

    ; ; DismissDialogs()
    ; ; Automatically dismisses any dialog boxes that appear during webcam operations.
    ; ; Specifically handles "Video Source" dialogs by selecting the configured camera.
    ; ; Also handles other video-related dialogs by clicking OK buttons or sending Enter.
    DismissDialogs() {
        static dialogClasses := ["#32770", "SysDialogClass", "DirectUIHWND"]

        ; Look for dialog windows
        for _, className in dialogClasses {
            dialogHwnd := WinExist("ahk_class " className)
            if (dialogHwnd) {
                ; Get the window title
                windowTitle := WinGetTitle("ahk_id " dialogHwnd)

                ; Specifically check for "Video Source" dialog
                if (windowTitle = "Video Source") {

                    ; Try to select the correct camera in the dropdown if available
                    if (this.ControlExist("ComboBox1", "ahk_id " dialogHwnd) && this.cameraName != "") {
                        ; Get the list of items in the dropdown
                        try {
                            ; Activate the window first
                            WinActivate("ahk_id " dialogHwnd)
                            Sleep(100)

                            ; Try to select our camera by name
                            Control := "ComboBox1"
                            ControlFocus(Control, "ahk_id " dialogHwnd)

                            ; Get the number of items in the ComboBox
                            itemCount := SendMessage(0x0146, 0, 0, Control, "ahk_id " dialogHwnd) ; CB_GETCOUNT

                            ; Try to find our camera in the list
                            found := false
                            Loop itemCount {
                                ; Get the text of each item
                                itemIndex := A_Index - 1
                                textLen := SendMessage(0x0149, itemIndex, 0, Control, "ahk_id " dialogHwnd) ; CB_GETLBTEXTLEN

                                if (textLen > 0) {
                                    ; Create a buffer for the text
                                    itemText := Buffer((textLen + 1) * 2, 0)
                                    SendMessage(0x0148, itemIndex, itemText.Ptr, Control, "ahk_id " dialogHwnd) ; CB_GETLBTEXT

                                    ; Convert to string
                                    itemStr := StrGet(itemText.Ptr)

                                    ; Check if this matches our saved camera
                                    if (itemStr = this.cameraName || InStr(itemStr, this.cameraName) || InStr(this.cameraName, itemStr)) {
                                        ; Select this item
                                        SendMessage(0x014E, itemIndex, 0, Control, "ahk_id " dialogHwnd) ; CB_SETCURSEL
                                        found := true
                                        break
                                    }
                                }
                            }

                            ; If we couldn't find a match, just select the first item
                            if (!found && itemCount > 0) {
                                SendMessage(0x014E, 0, 0, Control, "ahk_id " dialogHwnd) ; CB_SETCURSEL
                            }
                        } catch as err {
                            OutputDebug("Error selecting camera in dropdown: " err.Message)
                        }
                    }

                    ; Click the OK button (Button2)
                    if (this.ControlExist("Button2", "ahk_id " dialogHwnd)) {
                        Sleep(100)  ; Give time for the selection to register
                        ControlClick("Button2", "ahk_id " dialogHwnd)
                        return
                    }

                    ; If Button2 doesn't exist, try Button1
                    if (this.ControlExist("Button1", "ahk_id " dialogHwnd)) {
                        Sleep(100)
                        ControlClick("Button1", "ahk_id " dialogHwnd)
                        return
                    }

                    ; If no buttons found, try Enter key
                    WinActivate("ahk_id " dialogHwnd)
                    Sleep(100)
                    SendInput("{Enter}")
                    OutputDebug("Sent Enter key to Video Source dialog")
                    return
                }

                ; Check for other video-related dialogs
                else if (windowTitle ~= "i)video|camera|source|format|display") {
                    OutputDebug("Found video-related dialog: " windowTitle)

                    ; Try to find and click OK button - try Button2 first (common for OK)
                    if (this.ControlExist("Button2", "ahk_id " dialogHwnd)) {
                        ControlClick("Button2", "ahk_id " dialogHwnd)
                        OutputDebug("Clicked Button2 (OK)")
                        return
                    }

                    ; Then try Button1
                    if (this.ControlExist("Button1", "ahk_id " dialogHwnd)) {
                        ControlClick("Button1", "ahk_id " dialogHwnd)
                        OutputDebug("Clicked Button1")
                        return
                    }

                    ; If no buttons found, try Enter key
                    WinActivate("ahk_id " dialogHwnd)
                    Sleep(50)
                    SendInput("{Enter}")
                    OutputDebug("Sent Enter key to dialog")
                    return
                }
            }
        }
    }

    ; ; ControlExist(Control, WinTitle, WinText, ExcludeTitle, ExcludeText)
    ; ; Helper method to check if a control exists in a window.
    ; ; @param Control: The control to check for.
    ; ; @param WinTitle: The window title to check in.
    ; ; @param WinText: Optional window text to match.
    ; ; @param ExcludeTitle: Optional title to exclude.
    ; ; @param ExcludeText: Optional text to exclude.
    ; ; @return: True if the control exists, False otherwise.
    ControlExist(Control, WinTitle := "", WinText := "", ExcludeTitle := "", ExcludeText := "") {
        try {
            ControlGetPos(, , , , Control, WinTitle, WinText, ExcludeTitle, ExcludeText)
            return true
        } catch {
            return false
        }
    }

    ; ; UpdateStatus(message)
    ; ; Sends a status message string to the registered callback function, if one exists.
    ; ; @param message: The status message to send.
    UpdateStatus(message) {
        if (this.statusCallback) {
            this.statusCallback.Call(message)
        }
    }

    ; ; CleanUp(*)
    ; ; Releases all resources used by the WebcamManager.
    ; ; Stops the timer, disconnects the driver, destroys the window, frees the DLL,
    ; ; and deletes temporary files. Intended to be called on exit or destruction.
    ; ; @param *: Accepts any parameters (used for OnExit binding).
    CleanUp(*) {
        OutputDebug("CleanUp called")

        ; Stop the refresh timer if active
        if (this.refreshTimer) {
            try {
                OutputDebug("Stopping refresh timer in CleanUp")
                SetTimer(this.refreshTimer, 0)
                this.refreshTimer := 0
            } catch as err {
                OutputDebug("Error stopping refresh timer in CleanUp: " err.Message)
            }
        }

        ; Stop the dismiss dialogs timer if active
        if (this.dismissDialogsTimer) {
            try {
                OutputDebug("Stopping dismiss dialogs timer in CleanUp")
                SetTimer(this.dismissDialogsTimer, 0)
                this.dismissDialogsTimer := 0
            } catch as err {
                OutputDebug("Error stopping dismiss dialogs timer in CleanUp: " err.Message)
            }
        }

        ; Disconnect from webcam
        if (this.capHwnd) {
            try {
                ; Check if the window still exists
                if (DllCall("IsWindow", "Ptr", this.capHwnd)) {
                    OutputDebug("Disconnecting from webcam driver in CleanUp")
                    WM_CAP := 0x400
                    WM_CAP_DRIVER_DISCONNECT := WM_CAP + 11

                    ; Try to disconnect from the driver
                    try {
                        SendMessage(WM_CAP_DRIVER_DISCONNECT, 0, 0, , "ahk_id " this.capHwnd)
                        OutputDebug("Successfully disconnected from webcam driver in CleanUp")
                    } catch as err {
                        OutputDebug("Error disconnecting from webcam driver in CleanUp: " err.Message)
                    }

                    ; Try to destroy the window
                    try {
                        OutputDebug("Destroying webcam window in CleanUp")
                        DllCall("DestroyWindow", "Ptr", this.capHwnd)
                        OutputDebug("Successfully destroyed webcam window in CleanUp")
                    } catch as err {
                        OutputDebug("Error destroying webcam window in CleanUp: " err.Message)
                    }
                } else {
                    OutputDebug("Webcam window no longer exists in CleanUp, skipping disconnect")
                }
            } catch as err {
                OutputDebug("Error checking webcam window in CleanUp: " err.Message)
            }

            ; Reset the handle regardless of success
            this.capHwnd := 0
        }

        ; Release the library
        if (this.hModule) {
            try {
                OutputDebug("Releasing library in CleanUp")
                DllCall("FreeLibrary", "Ptr", this.hModule)
                this.hModule := 0
                OutputDebug("Successfully released library in CleanUp")
            } catch as err {
                OutputDebug("Error releasing library in CleanUp: " err.Message)
                this.hModule := 0
            }
        }

        ; Delete temporary files
        try {
            if (FileExist(this.captureFile)) {
                OutputDebug("Deleting temporary file in CleanUp: " this.captureFile)
                FileDelete(this.captureFile)
                OutputDebug("Successfully deleted temporary file in CleanUp")
            }
        } catch as err {
            OutputDebug("Error deleting temporary file in CleanUp: " err.Message)
        }

        OutputDebug("CleanUp completed")
    }

    ; ; Cap_CreateCaptureWindow(hWndParent, x, y, w, h)
    ; ; Internal helper function to create the webcam capture window using avicap32.dll.
    ; ; @param hWndParent: The handle of the parent window.
    ; ; @param x: The x-coordinate for the capture window.
    ; ; @param y: The y-coordinate for the capture window.
    ; ; @param w: The width for the capture window.
    ; ; @param h: The height for the capture window.
    ; ; @return: The handle (HWND) of the created capture window, or 0 on failure.
    Cap_CreateCaptureWindow(hWndParent, x, y, w, h) {
        WS_CHILD := 0x40000000
        WS_VISIBLE := 0x10000000

        lpszWindowName := "capture"

        capHandle := DllCall("avicap32.dll\capCreateCaptureWindowW"
                      , "Str", lpszWindowName
                      , "UInt", WS_VISIBLE | WS_CHILD ; dwStyle
                      , "Int", x
                      , "Int", y
                      , "Int", w
                      , "Int", h
                      , "UInt", hWndParent
                      , "Int", 0)

        Return capHandle
    }
}